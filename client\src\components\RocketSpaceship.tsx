/**
 * Rocket-style spaceship SVG component (static decorative version)
 */

import { useRef } from 'react';
import { COLOR_VARIANTS } from '@/constants';

interface RocketSpaceshipProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
}

export default function RocketSpaceship({
  className = '',
  size = 'md',
  onClick
}: RocketSpaceshipProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const rocketRef = useRef<SVGSVGElement>(null);
  const laserRef = useRef<SVGGElement>(null);
  const glowRef = useRef<SVGGElement>(null);
  const exhaustRef = useRef<SVGGElement>(null);

  // Size configurations
  const sizeConfig = {
    sm: { width: 56, height: 48, viewBox: '0 0 56 48' },
    md: { width: 72, height: 60, viewBox: '0 0 72 60' },
    lg: { width: 88, height: 72, viewBox: '0 0 88 72' }
  };

  const currentSize = sizeConfig[size];

  return (
    <div
      ref={containerRef}
      className={`inline-block ${className}`}
      style={{ filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3))' }}
    >
      <svg
        ref={rocketRef}
        width={currentSize.width}
        height={currentSize.height}
        viewBox={currentSize.viewBox}
        className="overflow-visible"
        style={{ transformOrigin: 'center center' }}
      >
        {/* Glow Effect */}
        <defs>
          <radialGradient id="rocket-glow" cx="50%" cy="50%" r="70%">
            <stop offset="0%" stopColor={COLOR_VARIANTS.PINK} stopOpacity="0.6" />
            <stop offset="50%" stopColor="#FF1A4D" stopOpacity="0.3" />
            <stop offset="100%" stopColor="transparent" stopOpacity="0" />
          </radialGradient>

          <linearGradient id="rocket-body" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#E8E8E8" />
            <stop offset="50%" stopColor={COLOR_VARIANTS.WHITE} />
            <stop offset="100%" stopColor="#D0D0D0" />
          </linearGradient>

          <linearGradient id="rocket-cockpit" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor={COLOR_VARIANTS.PINK} stopOpacity="0.8" />
            <stop offset="50%" stopColor="#FF1A4D" stopOpacity="0.4" />
            <stop offset="100%" stopColor={COLOR_VARIANTS.PINK} stopOpacity="0.2" />
          </linearGradient>

          <linearGradient id="laser-beam" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor={COLOR_VARIANTS.PINK} stopOpacity="0.8" />
            <stop offset="50%" stopColor="#FF1A4D" stopOpacity="0.6" />
            <stop offset="100%" stopColor={COLOR_VARIANTS.PINK} stopOpacity="0.3" />
          </linearGradient>
        </defs>

        {/* Glow Background */}
        <g ref={glowRef} opacity="0.4">
          <ellipse
            cx={currentSize.width / 2}
            cy={currentSize.height * 0.4}
            rx={currentSize.width * 0.6}
            ry={currentSize.height * 0.3}
            fill="url(#rocket-glow)"
          />
        </g>

        {/* Rocket Body */}
        <g>
          {/* Main Saucer */}
          <ellipse
            cx={currentSize.width / 2}
            cy={currentSize.height * 0.45}
            rx={currentSize.width * 0.35}
            ry={currentSize.height * 0.15}
            fill="url(#rocket-body)"
            stroke={COLOR_VARIANTS.PINK}
            strokeWidth="1"
          />

          {/* Cockpit (dome) */}
          <ellipse
            cx={currentSize.width / 2}
            cy={currentSize.height * 0.35}
            rx={currentSize.width * 0.2}
            ry={currentSize.height * 0.15}
            fill="url(#rocket-cockpit)"
            stroke={COLOR_VARIANTS.PINK}
            strokeWidth="1"
          />

          {/* Center Ring */}
          <ellipse
            cx={currentSize.width / 2}
            cy={currentSize.height * 0.45}
            rx={currentSize.width * 0.25}
            ry={currentSize.height * 0.08}
            fill="none"
            stroke={COLOR_VARIANTS.PINK}
            strokeWidth="1.5"
          />

          {/* Rocket Fins */}
          <polygon
            points={`${currentSize.width * 0.15},${currentSize.height * 0.45} ${currentSize.width * 0.25},${currentSize.height * 0.35} ${currentSize.width * 0.25},${currentSize.height * 0.55}`}
            fill={COLOR_VARIANTS.PINK}
            opacity="0.8"
          />
          <polygon
            points={`${currentSize.width * 0.85},${currentSize.height * 0.45} ${currentSize.width * 0.75},${currentSize.height * 0.35} ${currentSize.width * 0.75},${currentSize.height * 0.55}`}
            fill={COLOR_VARIANTS.PINK}
            opacity="0.8"
          />
        </g>

        {/* Static Exhaust Ports */}
        <g ref={exhaustRef}>
          <circle
            cx={currentSize.width * 0.35}
            cy={currentSize.height * 0.55}
            r="2"
            fill="#FF1A4D"
            opacity="0.8"
          />
          <circle
            cx={currentSize.width * 0.5}
            cy={currentSize.height * 0.58}
            r="2"
            fill={COLOR_VARIANTS.PINK}
            opacity="0.8"
          />
          <circle
            cx={currentSize.width * 0.65}
            cy={currentSize.height * 0.55}
            r="2"
            fill="#FF1A4D"
            opacity="0.8"
          />
        </g>
      </svg>
    </div>
  );
}

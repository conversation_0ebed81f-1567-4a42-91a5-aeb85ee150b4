<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5" />

    <!-- Primary Meta Tags -->
    <title><PERSON> - Creative Developer & Designer | Portfolio</title>
    <meta name="title" content="<PERSON> - Creative Developer & Designer | Portfolio" />
    <meta name="description" content="Portfolio of <PERSON>, a creative developer and designer specializing in innovative web experiences, interactive animations, and AI-first applications. Explore cutting-edge projects and modern web development." />
    <meta name="keywords" content="web developer, creative developer, designer, portfolio, React, TypeScript, GSAP, animations, AI applications, full-stack developer" />
    <meta name="author" content="<PERSON>" />
    <meta name="robots" content="index, follow" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://jaime-ryan-portfolio.com/" />
    <meta property="og:title" content="<PERSON> - Creative Developer & Designer | Portfolio" />
    <meta property="og:description" content="Portfolio showcasing innovative web experiences, interactive animations, and AI-first applications by creative developer <PERSON>." />
    <meta property="og:image" content="/og-image.jpg" />
    <meta property="og:site_name" content="Jaime Ryan Portfolio" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://jaime-ryan-portfolio.com/" />
    <meta property="twitter:title" content="Jaime Ryan - Creative Developer & Designer | Portfolio" />
    <meta property="twitter:description" content="Portfolio showcasing innovative web experiences, interactive animations, and AI-first applications." />
    <meta property="twitter:image" content="/twitter-image.jpg" />

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="icon" type="image/png" href="/favicon.png" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet" />

    <!-- Theme Color -->
    <meta name="theme-color" content="#1A1A1A" />
    <meta name="msapplication-TileColor" content="#1A1A1A" />

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": "Jaime Ryan",
      "jobTitle": "Creative Developer & Designer",
      "description": "Creative developer and designer specializing in innovative web experiences and interactive animations",
      "url": "https://jaime-ryan-portfolio.com",
      "sameAs": [
        "https://github.com/jaime-ryan",
        "https://linkedin.com/in/jaime-ryan"
      ],
      "knowsAbout": ["Web Development", "React", "TypeScript", "GSAP", "UI/UX Design", "AI Applications"]
    }
    </script>

    <!-- Performance and Security -->
    <link rel="dns-prefetch" href="//fonts.googleapis.com" />
    <link rel="dns-prefetch" href="//fonts.gstatic.com" />

    <style>
      /* Critical CSS for initial render */
      body {
        font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        overflow-x: hidden;
        background-color: #1A1A1A;
        color: #F5F5F5;
        margin: 0;
        padding: 0;
      }

      /* Loading state */
      #root {
        min-height: 100vh;
      }

      /* Accessibility improvements */
      @media (prefers-reduced-motion: reduce) {
        *, *::before, *::after {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }

      /* Focus styles */
      :focus-visible {
        outline: 2px solid #FF3366;
        outline-offset: 2px;
      }
    </style>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>

    <!-- Development banner (only in development) -->
    <script type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js"></script>
  </body>
</html>

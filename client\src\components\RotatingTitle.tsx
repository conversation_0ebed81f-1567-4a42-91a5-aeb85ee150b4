import { useEffect, useRef, useState, useCallback } from "react";
import { useGSAP } from '@/hooks/useGSAP';
import { useErrorHand<PERSON> } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import {
  ROTATING_TITLES,
  ANIMATION_DURATIONS,
  ANIMATION_EASINGS,
  COLOR_VARIANTS
} from '@/constants';

interface RotatingTitleProps {
  titles?: readonly string[];
  rotationInterval?: number;
  className?: string;
}

export default function RotatingTitle({
  titles = ROTATING_TITLES,
  rotationInterval = 4000,
  className = "",
}: RotatingTitleProps) {
  const titleRef = useRef<HTMLParagraphElement>(null);
  const [currentIndex, setCurrentIndex] = useState(0);
  const animationInitialized = useRef(false);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const { gsap, animate, isAvailable } = useGSAP();
  const { handleError } = useErrorHandler();

  const cycleTitles = useCallback(() => {
    try {
      if (!titleRef.current || !isAvailable) return;

      const accessibility = getAccessibilityConfig();

      if (accessibility.reduceMotion) {
        // Just update text without animation for reduced motion
        setCurrentIndex((prevIndex) => (prevIndex + 1) % titles.length);
        timeoutRef.current = setTimeout(cycleTitles, rotationInterval);
        return;
      }

      const timeline = gsap?.timeline({
        onComplete: () => {
          timeoutRef.current = setTimeout(cycleTitles, 3000);
        }
      });

      if (!timeline) return;

      // Exit animation
      timeline.to(titleRef.current, {
        opacity: 0,
        y: -10,
        filter: "blur(4px)",
        duration: ANIMATION_DURATIONS.NORMAL,
        ease: ANIMATION_EASINGS.POWER2_IN,
      });

      // Update text
      timeline.call(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % titles.length);
      });

      // Entrance animation
      timeline.fromTo(titleRef.current,
        {
          opacity: 0,
          y: 10,
          scale: 0.95,
          filter: "blur(4px)"
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          filter: "blur(0px)",
          textShadow: `0 0 20px rgba(255, 255, 255, 0.3), 0 0 40px ${COLOR_VARIANTS.BLUE}33`,
          duration: ANIMATION_DURATIONS.NORMAL,
          ease: ANIMATION_EASINGS.POWER2_OUT,
        }
      );

      // Remove glow
      timeline.to(titleRef.current, {
        textShadow: "none",
        duration: ANIMATION_DURATIONS.NORMAL,
        ease: ANIMATION_EASINGS.POWER2_OUT,
      }, "+=0.5");

    } catch (error) {
      handleError(error as Error);
    }
  }, [isAvailable, gsap, titles.length, rotationInterval, handleError]);

  const startTitleRotation = useCallback(() => {
    try {
      if (!titleRef.current || !isAvailable || animationInitialized.current) return;

      animationInitialized.current = true;

      // Set initial state
      animate(titleRef.current, {
        opacity: 1,
        y: 0,
        scale: 1,
        filter: "blur(0px)",
        textShadow: "none",
        duration: 0,
      });

      // Start rotation cycle
      timeoutRef.current = setTimeout(cycleTitles, rotationInterval);
    } catch (error) {
      handleError(error as Error);
    }
  }, [isAvailable, animate, cycleTitles, rotationInterval, handleError]);

  useEffect(() => {
    if (isAvailable) {
      startTitleRotation();
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [isAvailable, startTitleRotation]);

  return (
    <p
      ref={titleRef}
      className={`text-lg md:text-xl font-light tracking-widest text-muted-foreground uppercase mb-8 ${className}`}
      style={{
        willChange: 'transform, opacity, filter',
        transformStyle: 'preserve-3d'
      }}
      role="status"
      aria-live="polite"
      aria-label={`Current title: ${titles[currentIndex]}`}
    >
      {titles[currentIndex]}
    </p>
  );
}
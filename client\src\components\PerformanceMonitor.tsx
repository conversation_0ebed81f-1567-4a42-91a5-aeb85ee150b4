/**
 * Performance monitoring component for development
 */

import React, { useState, useEffect, useRef } from 'react';
import type { PerformanceMetrics } from '@/types';
import { createPerformanceMonitor, isPerformanceAcceptable, formatNumber } from '@/utils';
import { PERFORMANCE_THRESHOLDS } from '@/constants';

interface PerformanceMonitorProps {
  enabled?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  showDetails?: boolean;
}

export function PerformanceMonitor({
  enabled = process.env.NODE_ENV === 'development',
  position = 'top-right',
  showDetails = false,
}: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [isExpanded, setIsExpanded] = useState(showDetails);
  const [frameRate, setFrameRate] = useState<number>(0);
  const monitorRef = useRef<ReturnType<typeof createPerformanceMonitor>>();
  const frameCountRef = useRef<number>(0);
  const lastTimeRef = useRef<number>(0);

  useEffect(() => {
    if (!enabled) return;

    monitorRef.current = createPerformanceMonitor();
    monitorRef.current.start();

    // Frame rate monitoring
    const measureFrameRate = () => {
      const now = performance.now();
      frameCountRef.current++;

      if (now - lastTimeRef.current >= 1000) {
        setFrameRate(frameCountRef.current);
        frameCountRef.current = 0;
        lastTimeRef.current = now;
      }

      requestAnimationFrame(measureFrameRate);
    };

    measureFrameRate();

    // Update metrics periodically
    const interval = setInterval(() => {
      if (monitorRef.current) {
        const currentMetrics = monitorRef.current.end();
        setMetrics(currentMetrics);
        monitorRef.current.start(); // Restart monitoring
      }
    }, 1000);

    return () => {
      clearInterval(interval);
    };
  }, [enabled]);

  if (!enabled || !metrics) {
    return null;
  }

  const isPerformanceGood = isPerformanceAcceptable(metrics);
  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4',
  };

  return (
    <div
      className={`fixed ${positionClasses[position]} z-[9999] bg-black/80 backdrop-blur-sm text-white text-xs font-mono rounded-lg border border-white/20 transition-all duration-200 ${
        isExpanded ? 'p-3' : 'p-2'
      }`}
      style={{ minWidth: isExpanded ? '200px' : 'auto' }}
    >
      <div
        className="flex items-center justify-between cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center space-x-2">
          <div
            className={`w-2 h-2 rounded-full ${
              isPerformanceGood ? 'bg-green-400' : 'bg-red-400'
            }`}
          />
          <span>Performance</span>
        </div>
        <svg
          className={`w-3 h-3 transition-transform ${
            isExpanded ? 'rotate-180' : ''
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M19 9l-7 7-7-7"
          />
        </svg>
      </div>

      {isExpanded && (
        <div className="mt-2 space-y-1">
          <div className="flex justify-between">
            <span>FPS:</span>
            <span
              className={
                frameRate >= PERFORMANCE_THRESHOLDS.MIN_FRAME_RATE
                  ? 'text-green-400'
                  : 'text-red-400'
              }
            >
              {frameRate}
            </span>
          </div>
          
          <div className="flex justify-between">
            <span>Memory:</span>
            <span
              className={
                metrics.memoryUsage <= PERFORMANCE_THRESHOLDS.MAX_MEMORY_USAGE
                  ? 'text-green-400'
                  : 'text-red-400'
              }
            >
              {formatNumber(metrics.memoryUsage)}MB
            </span>
          </div>
          
          <div className="flex justify-between">
            <span>Render:</span>
            <span className="text-blue-400">
              {metrics.renderTime.toFixed(1)}ms
            </span>
          </div>

          {(performance as any).memory && (
            <>
              <div className="border-t border-white/20 my-1" />
              <div className="flex justify-between">
                <span>Heap:</span>
                <span className="text-yellow-400">
                  {formatNumber((performance as any).memory.usedJSHeapSize / 1024 / 1024)}MB
                </span>
              </div>
              <div className="flex justify-between">
                <span>Limit:</span>
                <span className="text-gray-400">
                  {formatNumber((performance as any).memory.jsHeapSizeLimit / 1024 / 1024)}MB
                </span>
              </div>
            </>
          )}

          <div className="border-t border-white/20 my-1" />
          <div className="text-center">
            <span
              className={`text-xs ${
                isPerformanceGood ? 'text-green-400' : 'text-red-400'
              }`}
            >
              {isPerformanceGood ? 'Good' : 'Poor'} Performance
            </span>
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * Hook for performance monitoring in components
 */
export function usePerformanceMonitor() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const monitorRef = useRef<ReturnType<typeof createPerformanceMonitor>>();

  const startMonitoring = () => {
    monitorRef.current = createPerformanceMonitor();
    monitorRef.current.start();
  };

  const endMonitoring = () => {
    if (monitorRef.current) {
      const currentMetrics = monitorRef.current.end();
      setMetrics(currentMetrics);
      return currentMetrics;
    }
    return null;
  };

  const isGoodPerformance = metrics ? isPerformanceAcceptable(metrics) : true;

  return {
    metrics,
    startMonitoring,
    endMonitoring,
    isGoodPerformance,
  };
}

import { useEffect, useRef, useCallback, useState } from "react";
import { useGS<PERSON> } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import {
  ANIMATION_DURATIONS,
  ANIMATION_DELAYS,
  ANIMATION_EASINGS,
  COLOR_VARIANTS,
  NAVIGATION_ITEMS,
  Z_INDEX
} from '@/constants';
import AnimatedLetters from "@/components/AnimatedLetters";
import ParticleBackground from "@/components/ParticleBackground";
import SpaceMouseEffects from "@/components/SpaceMouseEffects";
import RotatingTitle from "@/components/RotatingTitle";
import PortfolioSection from "@/components/PortfolioSection";
import AboutMeSection from "@/components/AboutMeSection";
import ContactSection from "@/components/ContactSection";
import UFOAnimation from "@/components/UFOAnimation";
import EnhancedExploreText from '@/components/EnhancedExploreText';

export default function Home() {
  const subtitleRef = useRef<HTMLDivElement>(null);
  const scrollIndicatorRef = useRef<HTMLDivElement>(null);
  const navDotsRef = useRef<HTMLDivElement>(null);
  const [activeSection, setActiveSection] = useState<string>('hero');
  const { gsap, animate, isAvailable } = useGSAP();
  const { handleError } = useErrorHandler();

  const initializePageAnimations = useCallback(() => {
    try {
      if (!isAvailable) return;

      const accessibility = getAccessibilityConfig();

      if (accessibility.reduceMotion) {
        // Set final states immediately for reduced motion
        if (subtitleRef.current) {
          animate(subtitleRef.current, { opacity: 1, y: 0, duration: 0 });
        }
        if (navDotsRef.current) {
          animate(navDotsRef.current, { opacity: 1, x: 0, duration: 0 });
        }
        if (scrollIndicatorRef.current) {
          animate(scrollIndicatorRef.current, { opacity: 1, y: 0, duration: 0 });
        }
        return;
      }

      const timeline = gsap?.timeline();
      if (!timeline) {
        // Fallback if timeline creation fails
        setTimeout(() => {
          if (navDotsRef.current) {
            navDotsRef.current.style.opacity = '1';
            navDotsRef.current.style.transform = 'translateY(-50%)';
          }
        }, 100);
        return;
      }

      // Set initial positions with proper transforms
      if (navDotsRef.current) {
        timeline.set(navDotsRef.current, {
          opacity: 0,
          x: 16 // translate-x-4
        });
      }

      timeline.set(scrollIndicatorRef.current, {
        y: 16 // equivalent to translate-y-4 (1rem = 16px)
      });

      // Animate subtitle after letters complete their initial animation
      timeline.to(subtitleRef.current, {
        opacity: 1,
        y: 0,
        duration: ANIMATION_DURATIONS.SLOW,
        ease: ANIMATION_EASINGS.POWER2_OUT
      }, 2.5);

      // Animate navigation dots
      timeline.to(navDotsRef.current, {
        opacity: 1,
        x: 0,
        duration: ANIMATION_DURATIONS.SLOW,
        ease: ANIMATION_EASINGS.POWER2_OUT
      }, 3);

      // Animate scroll indicator
      timeline.to(scrollIndicatorRef.current, {
        opacity: 1,
        y: 0,
        duration: ANIMATION_DURATIONS.SLOW,
        ease: ANIMATION_EASINGS.POWER2_OUT
      }, 3.5);

      // Safety fallback - ensure navigation dots are visible after 5 seconds
      timeline.call(() => {
        if (navDotsRef.current && navDotsRef.current.style.opacity !== '1') {
          navDotsRef.current.style.opacity = '1';
          navDotsRef.current.style.transform = 'translateY(-50%)';
        }
      }, [], 5);

    } catch (error) {
      handleError(error as Error);
      // Emergency fallback
      setTimeout(() => {
        if (navDotsRef.current) {
          navDotsRef.current.style.opacity = '1';
          navDotsRef.current.style.transform = 'translateY(-50%)';
        }
      }, 100);
    }
  }, [isAvailable, gsap, animate, handleError]);

  useEffect(() => {
    if (isAvailable) {
      initializePageAnimations();
    } else {
      // Fallback: Ensure elements are visible if GSAP is not available
      if (navDotsRef.current) {
        navDotsRef.current.style.opacity = '1';
        navDotsRef.current.style.transform = 'translateY(-50%)';
      }
      if (subtitleRef.current) {
        subtitleRef.current.style.opacity = '1';
        subtitleRef.current.style.transform = 'translateY(0)';
      }
      if (scrollIndicatorRef.current) {
        scrollIndicatorRef.current.style.opacity = '1';
        scrollIndicatorRef.current.style.transform = 'translateX(-50%)';
      }
    }
  }, [isAvailable, initializePageAnimations]);

  const handleNavDotClick = useCallback((index: number) => {
    try {
      const sectionIds = ['#portfolio', '#about', '#contact'];
      const targetSection = sectionIds[index];
      
      const element = document.querySelector(targetSection);
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    } catch (error) {
      handleError(error as Error);
    }
  }, [handleError]);

  // Add scroll listener to track active section
  useEffect(() => {
    const handleScroll = () => {
      try {
        const sections = [
          { id: 'hero', element: document.querySelector('main') },
          { id: 'portfolio', element: document.querySelector('#portfolio') },
          { id: 'about', element: document.querySelector('#about') },
          { id: 'contact', element: document.querySelector('#contact') }
        ];

        const scrollPosition = window.scrollY + window.innerHeight / 2;

        for (let i = sections.length - 1; i >= 0; i--) {
          const section = sections[i];
          if (section.element) {
            const rect = section.element.getBoundingClientRect();
            const elementTop = rect.top + window.scrollY;
            
            if (scrollPosition >= elementTop) {
              setActiveSection(section.id);
              break;
            }
          }
        }
      } catch (error) {
        handleError(error as Error);
      }
    };

    // Run immediately to set initial state
    handleScroll();

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleError]);

  const handleNavigationClick = useCallback((href: string) => {
    const element = document.querySelector(href);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  }, []);

  return (
    <>
      <div className="min-h-screen bg-background font-montserrat overflow-hidden relative">
        {/* Background with gradient */}
        <div
          className="fixed inset-0 bg-gradient-radial"
          style={{ zIndex: Z_INDEX.BACKGROUND }}
          role="presentation"
          aria-hidden="true"
        />

        {/* Space Mouse Effects - Parallax Stars & Cursor Trail */}
        <SpaceMouseEffects
          enableParallax={true}
          enableCursorTrail={true}
        />

        {/* Particle Background */}
        <ParticleBackground />

        {/* UFO Animation */}
        <UFOAnimation />

        {/* Main Content */}
        <main
          className="relative min-h-screen flex items-center justify-center"
          style={{ zIndex: Z_INDEX.CONTENT }}
          role="main"
        >
          <div className="text-center px-4 max-w-4xl mx-auto">
            {/* Main Typography */}
            <header className="mb-8">
              <AnimatedLetters />
            </header>

            {/* Subtitle */}
            <div
              ref={subtitleRef}
              className="opacity-0 transform translate-y-4"
            >
              <RotatingTitle />

              {/* Navigation Links */}
              <nav
                className="flex justify-center space-x-8 text-sm"
                role="navigation"
                aria-label="Main navigation"
              >
                {NAVIGATION_ITEMS.map((item, index) => (
                  <button
                    key={item.href}
                    onClick={() => handleNavigationClick(item.href)}
                    className="text-foreground hover:text-primary transition-colors duration-300 tracking-wider uppercase focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background"
                    style={{
                      '--hover-color': item.color
                    } as React.CSSProperties}
                    aria-label={`Navigate to ${item.label} section`}
                  >
                    {item.label}
                  </button>
                ))}
              </nav>
            </div>
          </div>
        </main>

        {/* Navigation Dots */}
        <aside
          ref={navDotsRef}
          className="fixed right-8 top-1/2 transform -translate-y-1/2 flex flex-col space-y-6"
          style={{ 
            zIndex: Z_INDEX.NAVIGATION,
            opacity: 1, // Ensure visible by default
          }}
          role="complementary"
          aria-label="Section navigation"
        >
          {[
            { section: 'portfolio', label: 'Portfolio', color: COLOR_VARIANTS.PINK },
            { section: 'about', label: 'About', color: COLOR_VARIANTS.BLUE },
            { section: 'contact', label: 'Contact', color: COLOR_VARIANTS.WHITE }
          ].map((nav, index) => {
            const isActive = activeSection === nav.section;
            return (
              <div key={nav.section} className="flex items-center group">
                <button
                  className={`w-4 h-4 rounded-full cursor-pointer hover:scale-125 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-background border-2 ${
                    isActive 
                      ? 'border-white scale-110' 
                      : 'border-white/50 hover:border-white/80'
                  }`}
                  style={{ 
                    backgroundColor: isActive ? nav.color : 'rgba(255, 255, 255, 0.2)',
                    boxShadow: isActive ? `0 0 12px ${nav.color}` : 'none'
                  }}
                  onClick={() => handleNavDotClick(index)}
                  aria-label={`Navigate to ${nav.label} section`}
                  title={nav.label}
                />
                <span 
                  className={`ml-4 text-sm uppercase tracking-wider transition-all duration-300 opacity-0 group-hover:opacity-100 translate-x-2 group-hover:translate-x-0 ${
                    isActive ? 'text-white' : 'text-white/70'
                  }`}
                  style={{ color: isActive ? nav.color : undefined }}
                >
                  {nav.label}
                </span>
              </div>
            );
          })}
        </aside>
      </div>

      {/* Scroll Indicator - moved outside the main container */}
      <div
        ref={scrollIndicatorRef}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 opacity-0"
        style={{ zIndex: Z_INDEX.CONTENT }}
        role="presentation"
        aria-hidden="true"
      >
        <div className="flex flex-col items-center space-y-2">
          <EnhancedExploreText />
        </div>
      </div>

      {/* Portfolio Section */}
      <PortfolioSection />

      {/* About Me Section */}
      <AboutMeSection />

      {/* Contact Section */}
      <ContactSection />
    </>
  );
}

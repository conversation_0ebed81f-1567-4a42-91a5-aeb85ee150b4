/**
 * Space-themed mouse effects component with parallax stars and cursor trail
 */

import { useEffect, useRef, useCallback } from 'react';
import { useMouseParallax } from '@/hooks/useMouseParallax';
import { useCursorTrail } from '@/hooks/useCursorTrail';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import { MOUSE_EFFECTS_CONFIG, Z_INDEX } from '@/constants';

interface SpaceMouseEffectsProps {
  enableParallax?: boolean;
  enableCursorTrail?: boolean;
  className?: string;
}

interface StarLayer {
  container: HTMLDivElement;
  stars: HTMLDivElement[];
  intensity: number;
}

export default function SpaceMouseEffects({
  enableParallax = true,
  enableCursorTrail = true,
  className = "",
}: SpaceMouseEffectsProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const starLayersRef = useRef<StarLayer[]>([]);
  const layerIndicesRef = useRef<number[]>([]);
  
  const { registerLayer, unregisterLayer, start: startParallax, stop: stopParallax } = useMouseParallax();
  const { start: startCursorTrail, stop: stopCursorTrail, setEnabled: setCursorTrailEnabled } = useCursorTrail({
    enabled: enableCursorTrail,
  });
  const { handleError } = useErrorHandler();

  const createStar = useCallback((layerIndex: number): HTMLDivElement => {
    const star = document.createElement('div');
    const { SIZE_RANGE, OPACITY_RANGE, COLORS } = MOUSE_EFFECTS_CONFIG.STARS;
    
    const size = SIZE_RANGE[0] + Math.random() * (SIZE_RANGE[1] - SIZE_RANGE[0]);
    const opacity = OPACITY_RANGE[0] + Math.random() * (OPACITY_RANGE[1] - OPACITY_RANGE[0]);
    const color = COLORS[Math.floor(Math.random() * COLORS.length)];
    
    // Position randomly across the viewport
    const x = Math.random() * window.innerWidth;
    const y = Math.random() * window.innerHeight;
    
    star.style.cssText = `
      position: absolute;
      width: ${size}px;
      height: ${size}px;
      background: ${color};
      border-radius: 50%;
      left: ${x}px;
      top: ${y}px;
      pointer-events: none;
      opacity: ${opacity};
      box-shadow: 0 0 ${size * 2}px ${color}60;
      will-change: transform;
    `;
    
    return star;
  }, []);

  const createStarLayer = useCallback((
    layerIndex: number,
    starCount: number,
    intensity: number
  ): StarLayer => {
    try {
      const container = document.createElement('div');
      container.style.cssText = `
        position: fixed;
        inset: 0;
        pointer-events: none;
        z-index: ${Z_INDEX.PARALLAX_STARS + layerIndex};
        overflow: hidden;
      `;
      
      const stars: HTMLDivElement[] = [];
      for (let i = 0; i < starCount; i++) {
        const star = createStar(layerIndex);
        container.appendChild(star);
        stars.push(star);
      }
      
      return {
        container,
        stars,
        intensity,
      };
    } catch (error) {
      handleError(error as Error);
      throw error;
    }
  }, [createStar, handleError]);

  const initializeStarLayers = useCallback(() => {
    try {
      if (!containerRef.current || !enableParallax) return;

      const accessibility = getAccessibilityConfig();
      if (accessibility.reduceMotion) return;

      // Clear existing layers
      starLayersRef.current.forEach(layer => {
        layer.container.remove();
      });
      starLayersRef.current = [];
      
      layerIndicesRef.current.forEach(index => {
        unregisterLayer(index);
      });
      layerIndicesRef.current = [];

      const { LAYER_1_COUNT, LAYER_2_COUNT, LAYER_3_COUNT } = MOUSE_EFFECTS_CONFIG.STARS;
      const layerConfigs = [
        { count: LAYER_1_COUNT, intensity: 1.0 },    // Closest layer - most movement
        { count: LAYER_2_COUNT, intensity: 0.6 },    // Middle layer
        { count: LAYER_3_COUNT, intensity: 0.3 },    // Farthest layer - least movement
      ];

      layerConfigs.forEach((config, index) => {
        const layer = createStarLayer(index, config.count, config.intensity);
        starLayersRef.current.push(layer);
        containerRef.current!.appendChild(layer.container);

        // Register layer with parallax system
        const layerIndex = registerLayer(layer.stars, config.intensity);
        layerIndicesRef.current.push(layerIndex);
      });

    } catch (error) {
      handleError(error as Error);
    }
  }, [enableParallax, createStarLayer, registerLayer, unregisterLayer, handleError]);

  const handleResize = useCallback(() => {
    try {
      // Recreate stars on resize to maintain proper distribution
      const timeoutId = setTimeout(() => {
        initializeStarLayers();
      }, 250); // Debounce resize events

      return () => clearTimeout(timeoutId);
    } catch (error) {
      handleError(error as Error);
    }
  }, [initializeStarLayers, handleError]);

  const startEffects = useCallback(() => {
    try {
      const accessibility = getAccessibilityConfig();
      if (accessibility.reduceMotion) return;

      if (enableParallax) {
        initializeStarLayers();
        startParallax();
      }

      if (enableCursorTrail) {
        startCursorTrail(document.body);
      }
    } catch (error) {
      handleError(error as Error);
    }
  }, [enableParallax, enableCursorTrail, initializeStarLayers, startParallax, startCursorTrail, handleError]);

  const stopEffects = useCallback(() => {
    try {
      stopParallax();
      stopCursorTrail();
    } catch (error) {
      handleError(error as Error);
    }
  }, [stopParallax, stopCursorTrail, handleError]);

  // Initialize effects on mount
  useEffect(() => {
    startEffects();

    // Add resize listener
    window.addEventListener('resize', handleResize);

    return () => {
      stopEffects();
      window.removeEventListener('resize', handleResize);
      
      // Cleanup star layers
      starLayersRef.current.forEach(layer => {
        layer.container.remove();
      });
      starLayersRef.current = [];
      
      layerIndicesRef.current.forEach(index => {
        unregisterLayer(index);
      });
      layerIndicesRef.current = [];
    };
  }, [startEffects, stopEffects, handleResize, unregisterLayer]);

  // Update effects when props change
  useEffect(() => {
    setCursorTrailEnabled(enableCursorTrail);
  }, [enableCursorTrail, setCursorTrailEnabled]);

  useEffect(() => {
    if (enableParallax) {
      initializeStarLayers();
      startParallax();
    } else {
      stopParallax();
      // Remove star layers
      starLayersRef.current.forEach(layer => {
        layer.container.remove();
      });
      starLayersRef.current = [];
    }
  }, [enableParallax, initializeStarLayers, startParallax, stopParallax]);

  return (
    <div
      ref={containerRef}
      className={`fixed inset-0 pointer-events-none ${className}`}
      style={{ zIndex: Z_INDEX.PARALLAX_STARS }}
      role="presentation"
      aria-hidden="true"
    />
  );
}

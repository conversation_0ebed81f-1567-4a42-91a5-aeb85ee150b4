/**
 * Contact section component with spaceship animations and laser-traced heading
 */

import { useEffect, useRef, useCallback, useState } from "react";
import { useGS<PERSON> } from '@/hooks/useGSAP';
import { useScrollTrigger } from '@/hooks/useGSAP';
import { use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import RocketSpaceship from '@/components/RocketSpaceship';
import UFOSpaceship from '@/components/UFOSpaceship';
import {
  ANIMATION_DURATIONS,
  ANIMATION_EASINGS,
  COLOR_VARIANTS,
  CONTACT_CONFIG,
  Z_INDEX
} from '@/constants';

interface FormData {
  name: string;
  email: string;
  message: string;
}

export default function ContactSection() {
  const sectionRef = useRef<HTMLElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const alienTitleRef = useRef<HTMLHeadingElement>(null);
  const leftSpaceshipRef = useRef<HTMLDivElement>(null);
  const rightSpaceshipRef = useRef<HTMLDivElement>(null);
  const laserPathRef = useRef<SVGPathElement>(null);
  const formRef = useRef<HTMLFormElement>(null);
  const invitationRef = useRef<HTMLParagraphElement>(null);
  const hasAnimated = useRef(false);

  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    message: ''
  });

  const { gsap, animate, isAvailable } = useGSAP();
  const { createScrollTrigger } = useScrollTrigger();
  const { handleError } = useErrorHandler();

  const handleInputChange = useCallback((
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  }, []);

  const handleEmailClick = useCallback(() => {
    const subject = encodeURIComponent('Hello from your portfolio website');
    const body = encodeURIComponent(`Hi Jaime,\n\nI visited your portfolio website and would like to get in touch.\n\nName: ${formData.name || '[Not provided]'}\nEmail: ${formData.email || '[Not provided]'}\n\nMessage:\n${formData.message || '[No message provided]'}\n\nBest regards,\n${formData.name || '[Your Name]'}`);
    window.location.href = `mailto:${CONTACT_CONFIG.EMAIL_ADDRESS}?subject=${subject}&body=${body}`;
  }, [formData.name, formData.email, formData.message]);

  const animateSpaceships = useCallback(() => {
    try {
      if (!isAvailable || !gsap || !leftSpaceshipRef.current || !rightSpaceshipRef.current) return;

      const accessibility = getAccessibilityConfig();
      if (accessibility.reduceMotion) {
        // Set final positions for reduced motion
        gsap.set([leftSpaceshipRef.current, rightSpaceshipRef.current], {
          opacity: 1,
          x: 0,
          y: 0
        });
        return;
      }

      const timeline = gsap.timeline();

      // Set initial positions (off-screen)
      timeline.set(leftSpaceshipRef.current, {
        x: -250,
        y: 60,
        rotation: 20,
        opacity: 0,
        scale: 0.8
      });

      timeline.set(rightSpaceshipRef.current, {
        x: 250,
        y: -60,
        rotation: -20,
        opacity: 0,
        scale: 0.8
      });

      // Animate spaceships flying in with enhanced effects
      timeline.to(leftSpaceshipRef.current, {
        x: 0,
        y: 0,
        rotation: 0,
        opacity: 1,
        scale: 1,
        duration: CONTACT_CONFIG.SPACESHIP_FLIGHT_DURATION,
        ease: ANIMATION_EASINGS.BACK_OUT,
      }, 0);

      timeline.to(rightSpaceshipRef.current, {
        x: 0,
        y: 0,
        rotation: 0,
        opacity: 1,
        scale: 1,
        duration: CONTACT_CONFIG.SPACESHIP_FLIGHT_DURATION,
        ease: ANIMATION_EASINGS.BACK_OUT,
      }, 0.3);

      // Add enhanced floating animations after arrival
      timeline.to(leftSpaceshipRef.current, {
        y: -CONTACT_CONFIG.SPACESHIP_FLOAT_AMPLITUDE,
        rotation: 3,
        duration: 3,
        ease: "power2.inOut",
        repeat: -1,
        yoyo: true,
      }, `+=${ANIMATION_DURATIONS.FAST}`);

      timeline.to(rightSpaceshipRef.current, {
        y: CONTACT_CONFIG.SPACESHIP_FLOAT_AMPLITUDE,
        rotation: -3,
        duration: 3.5,
        ease: "power2.inOut",
        repeat: -1,
        yoyo: true,
      }, `+=${ANIMATION_DURATIONS.FAST}`);

      // Add subtle horizontal drift
      timeline.to(leftSpaceshipRef.current, {
        x: -5,
        duration: 4,
        ease: "power1.inOut",
        repeat: -1,
        yoyo: true,
      }, `+=${ANIMATION_DURATIONS.NORMAL}`);

      timeline.to(rightSpaceshipRef.current, {
        x: 5,
        duration: 4.5,
        ease: "power1.inOut",
        repeat: -1,
        yoyo: true,
      }, `+=${ANIMATION_DURATIONS.NORMAL}`);

      return timeline;
    } catch (error) {
      handleError(error as Error);
    }
  }, [isAvailable, gsap, handleError]);

  const animateLaserTrace = useCallback(() => {
    try {
      if (!isAvailable || !gsap || !laserPathRef.current || !titleRef.current) return;

      const accessibility = getAccessibilityConfig();
      if (accessibility.reduceMotion) {
        // Show final state immediately
        gsap.set(titleRef.current, { opacity: 1 });
        gsap.set(laserPathRef.current, { strokeDashoffset: 0 });
        return;
      }

      const timeline = gsap.timeline();
      const path = laserPathRef.current;
      const pathLength = path.getTotalLength();

      // Set up the laser path
      timeline.set(path, {
        strokeDasharray: pathLength,
        strokeDashoffset: pathLength,
        opacity: 1
      });

      // Hide title initially
      timeline.set(titleRef.current, { opacity: 0 });

      // Animate laser tracing the text
      timeline.to(path, {
        strokeDashoffset: 0,
        duration: CONTACT_CONFIG.LASER_TRACE_DURATION,
        ease: ANIMATION_EASINGS.POWER2_IN_OUT,
      });

      // Show the title text as laser completes
      timeline.to(titleRef.current, {
        opacity: 1,
        duration: ANIMATION_DURATIONS.NORMAL,
        ease: ANIMATION_EASINGS.POWER2_OUT,
      }, `-=${ANIMATION_DURATIONS.NORMAL}`);

      // Fade out the laser path
      timeline.to(path, {
        opacity: 0,
        duration: ANIMATION_DURATIONS.NORMAL,
        ease: ANIMATION_EASINGS.POWER2_OUT,
      }, `+=${ANIMATION_DURATIONS.FAST}`);

      return timeline;
    } catch (error) {
      handleError(error as Error);
    }
  }, [isAvailable, gsap, handleError]);

  const animateAlienText = useCallback(() => {
    try {
      if (!isAvailable || !gsap || !alienTitleRef.current) return;

      const accessibility = getAccessibilityConfig();
      if (accessibility.reduceMotion) {
        // Skip alien animation for reduced motion
        gsap.set(alienTitleRef.current, { opacity: 0 });
        return;
      }

      const timeline = gsap.timeline();
      const alienGlyphs = ['◊', '◈', '◇', '◉', '⬢', '⬡', '⟐', '⟡'];
      const originalText = 'CONTACT ME';
      const letters = originalText.split('').map(char => char === ' ' ? ' ' : char);

      // Phase 1: Show alien text
      timeline.set(alienTitleRef.current, { opacity: 1 });

      // Transform each letter to alien glyph with stagger
      letters.forEach((letter, index) => {
        if (letter !== ' ') {
          const randomGlyph = alienGlyphs[Math.floor(Math.random() * alienGlyphs.length)];
          timeline.call(() => {
            if (alienTitleRef.current) {
              const spans = alienTitleRef.current.querySelectorAll('.alien-letter');
              if (spans[index]) {
                spans[index].textContent = randomGlyph;
                (spans[index] as HTMLElement).style.color = '#4A90E2';
                (spans[index] as HTMLElement).style.textShadow = '0 0 20px rgba(74, 144, 226, 0.8)';
              }
            }
          }, [], index * 0.1);

          // Add glitch effect to each letter
          if (alienTitleRef.current) {
            const letterElement = alienTitleRef.current.querySelectorAll('.alien-letter')[index];
            if (letterElement) {
              timeline.to(letterElement, {
                scale: 1.2,
                rotation: Math.random() * 20 - 10,
                duration: 0.1,
                ease: "power2.out"
              }, index * 0.1 + 0.05);

              timeline.to(letterElement, {
                scale: 1,
                rotation: 0,
                duration: 0.1,
                ease: "power2.out"
              }, index * 0.1 + 0.15);
            }
          }
        }
      });

      // Phase 2: Hold alien text
      timeline.to({}, { duration: CONTACT_CONFIG.ALIEN_TEXT_DISPLAY_DURATION });

      // Phase 3: Particle explosion
      timeline.call(() => {
        createParticleExplosion();
      });

      // Hide alien text during explosion
      timeline.to(alienTitleRef.current, {
        opacity: 0,
        scale: 1.5,
        filter: "blur(5px) brightness(2)",
        duration: CONTACT_CONFIG.PARTICLE_EXPLOSION_DURATION * 0.3,
        ease: "power2.out"
      });

      // Wait for particle animation to complete
      timeline.to({}, { duration: CONTACT_CONFIG.PARTICLE_EXPLOSION_DURATION });

      return timeline;
    } catch (error) {
      handleError(error as Error);
    }
  }, [isAvailable, gsap, handleError]);

  const createParticleExplosion = () => {
    if (!alienTitleRef.current || !gsap) return;

    const rect = alienTitleRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    const particles: HTMLElement[] = [];
    const particleCount = 20;

    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'alien-explosion-particle';

      const size = 4 + Math.random() * 8;
      const hue = 180 + Math.random() * 60; // Blue to cyan range

      particle.style.cssText = `
        position: fixed;
        width: ${size}px;
        height: ${size}px;
        background: hsl(${hue}, 80%, 60%);
        border-radius: 50%;
        left: ${centerX}px;
        top: ${centerY}px;
        pointer-events: none;
        z-index: 100;
        box-shadow: 0 0 10px hsl(${hue}, 80%, 60%);
      `;

      document.body.appendChild(particle);
      particles.push(particle);

      // Animate particle explosion
      const angle = (i / particleCount) * Math.PI * 2;
      const distance = 100 + Math.random() * 100;
      const endX = centerX + Math.cos(angle) * distance;
      const endY = centerY + Math.sin(angle) * distance;

      gsap.to(particle, {
        x: endX - centerX,
        y: endY - centerY,
        opacity: 0,
        scale: 0,
        duration: CONTACT_CONFIG.PARTICLE_EXPLOSION_DURATION,
        ease: "power2.out",
        onComplete: () => {
          particle.remove();
        }
      });
    }
  };

  const animateContent = useCallback(() => {
    try {
      if (!isAvailable || !gsap) return;

      const accessibility = getAccessibilityConfig();
      if (accessibility.reduceMotion) {
        // Set final states for reduced motion
        const elements = [invitationRef.current, formRef.current];
        elements.forEach(el => {
          if (el) gsap.set(el, { opacity: 1, y: 0 });
        });
        return;
      }

      const timeline = gsap.timeline();
      const elements = [invitationRef.current];
      const formFields = formRef.current?.querySelectorAll('.form-field');

      // Animate invitation text
      elements.forEach((el, index) => {
        if (el) {
          timeline.fromTo(el,
            { opacity: 0, y: 30 },
            {
              opacity: 1,
              y: 0,
              duration: ANIMATION_DURATIONS.SLOW,
              ease: ANIMATION_EASINGS.POWER2_OUT,
            },
            index * CONTACT_CONFIG.FORM_ANIMATION_STAGGER
          );
        }
      });

      // Animate form fields with stagger
      if (formFields) {
        timeline.fromTo(formFields,
          { opacity: 0, y: 20 },
          {
            opacity: 1,
            y: 0,
            duration: ANIMATION_DURATIONS.NORMAL,
            ease: ANIMATION_EASINGS.POWER2_OUT,
            stagger: CONTACT_CONFIG.FORM_ANIMATION_STAGGER,
          },
          0.5
        );
      }

      return timeline;
    } catch (error) {
      handleError(error as Error);
    }
  }, [isAvailable, gsap, handleError]);

  const initializeAnimations = useCallback(() => {
    try {
      if (!isAvailable || hasAnimated.current || !sectionRef.current) return;

      hasAnimated.current = true;

      const masterTimeline = gsap?.timeline();
      if (!masterTimeline) return;

      // Sequence the animations with proper timing
      const spaceshipAnimation = animateSpaceships();
      const alienTextAnimation = animateAlienText();
      const laserAnimation = animateLaserTrace();
      const contentAnimation = animateContent();

      // Timeline sequence:
      // 0s: Spaceships fly in (2s duration)
      // 2.5s: Alien text appears and animates (2s + 1.5s + 1s = 4.5s total)
      // 7s: Laser trace and real "CONTACT ME" text (1.5s duration)
      // 9s: Content (invitation + form) animates in
      if (spaceshipAnimation) masterTimeline.add(spaceshipAnimation, 0);
      if (alienTextAnimation) masterTimeline.add(alienTextAnimation, 2.5);
      if (laserAnimation) masterTimeline.add(laserAnimation, 7.0);
      if (contentAnimation) masterTimeline.add(contentAnimation, 9.0);

    } catch (error) {
      handleError(error as Error);
    }
  }, [isAvailable, gsap, animateSpaceships, animateAlienText, animateLaserTrace, animateContent, handleError]);

  useEffect(() => {
    if (!isAvailable || !sectionRef.current) return;

    const trigger = createScrollTrigger({
      trigger: sectionRef.current,
      start: "top 80%",
      once: true,
      onEnter: initializeAnimations,
    });

    return () => {
      if (trigger) trigger.kill();
    };
  }, [isAvailable, createScrollTrigger, initializeAnimations]);

  return (
    <section
      ref={sectionRef}
      className="min-h-screen bg-[#0F0F0F] py-20 px-4 relative overflow-hidden"
      id="contact"
      role="region"
      aria-labelledby="contact-heading"
    >
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#1A1A1A] via-[#0F0F0F] to-[#1A1A1A] opacity-80" />

      <div className="max-w-4xl mx-auto relative" style={{ zIndex: Z_INDEX.CONTENT }}>
        {/* Title Section with Spaceships and Laser Effect */}
        <div className="text-center mb-16 relative">
          {/* Left Spaceship - Rocket */}
          <div
            ref={leftSpaceshipRef}
            className="absolute left-0 md:left-4 lg:left-8 top-1/2 transform -translate-y-1/2 opacity-0"
            style={{ zIndex: Z_INDEX.CONTENT + 1 }}
          >
            <RocketSpaceship
              size="lg"
              className="w-12 h-16 md:w-16 md:h-20 lg:w-20 lg:h-24"
            />
          </div>

          {/* Right Spaceship - UFO */}
          <div
            ref={rightSpaceshipRef}
            className="absolute right-0 md:right-4 lg:right-8 top-1/2 transform -translate-y-1/2 opacity-0"
            style={{ zIndex: Z_INDEX.CONTENT + 1 }}
          >
            <UFOSpaceship
              size="lg"
              className="w-14 h-12 md:w-18 md:h-15 lg:w-22 lg:h-18"
            />
          </div>

          {/* Alien Title (appears first) */}
          <div className="relative">
            <h2
              ref={alienTitleRef}
              className="text-4xl md:text-5xl lg:text-6xl font-black text-[#F5F5F5] tracking-wide opacity-0 absolute inset-0"
              aria-hidden="true"
            >
              <span className="alien-letter inline-block">C</span>
              <span className="alien-letter inline-block">O</span>
              <span className="alien-letter inline-block">N</span>
              <span className="alien-letter inline-block">T</span>
              <span className="alien-letter inline-block">A</span>
              <span className="alien-letter inline-block">C</span>
              <span className="alien-letter inline-block">T</span>
              <span className="alien-letter inline-block"> </span>
              <span className="alien-letter inline-block">M</span>
              <span className="alien-letter inline-block">E</span>
            </h2>

            {/* Title with Laser Trace Effect */}
            <h2
              ref={titleRef}
              id="contact-heading"
              className="text-4xl md:text-5xl lg:text-6xl font-black text-[#F5F5F5] tracking-wide opacity-0"
            >
              CONTACT ME
            </h2>

            {/* SVG Laser Path Overlay */}
            <svg
              className="absolute inset-0 w-full h-full pointer-events-none"
              style={{ zIndex: Z_INDEX.CONTENT + 2 }}
              viewBox="0 0 800 120"
            >
              <path
                ref={laserPathRef}
                d="M 50 60 L 120 60 L 120 30 L 150 30 L 150 90 L 120 90 L 120 60 M 180 30 L 180 90 M 180 45 L 220 45 M 180 75 L 210 75 M 250 30 L 250 90 L 290 90 L 290 60 L 270 60 M 320 30 L 360 30 L 360 90 L 320 90 L 320 60 L 350 60 M 390 30 L 430 30 M 410 30 L 410 90 M 460 30 L 460 90 M 460 45 L 500 45 M 460 75 L 490 75 M 530 30 L 530 90 L 570 90 L 570 60 L 550 60 M 600 30 L 600 90 M 600 45 L 640 45 M 600 75 L 630 75 M 670 30 L 670 90 L 710 90 L 710 60 L 690 60"
                stroke={COLOR_VARIANTS.BLUE}
                strokeWidth="2"
                fill="none"
                opacity="0"
                filter="drop-shadow(0 0 8px #4A90E2)"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
        </div>

        {/* Invitation Text */}
        <p
          ref={invitationRef}
          className="text-center text-lg md:text-xl text-gray-300 mb-12 opacity-0"
        >
          Ready to bring your ideas to life? Let's create something amazing together.
        </p>

        {/* Contact Form */}
        <form
          ref={formRef}
          className="max-w-2xl mx-auto space-y-6"
          onSubmit={(e) => e.preventDefault()}
        >
          {/* Name Field */}
          <div className="form-field opacity-0">
            <label
              htmlFor="contact-name"
              className="block text-sm font-medium text-gray-300 mb-2"
            >
              Name
            </label>
            <input
              type="text"
              id="contact-name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="w-full px-4 py-3 bg-[#1A1A1A] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#4A90E2] focus:border-transparent transition-all duration-300"
              placeholder="Your name"
            />
          </div>

          {/* Email Field */}
          <div className="form-field opacity-0">
            <label
              htmlFor="contact-email"
              className="block text-sm font-medium text-gray-300 mb-2"
            >
              Email <span className="text-[#FF3366]">*</span>
            </label>
            <input
              type="email"
              id="contact-email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              required
              className="w-full px-4 py-3 bg-[#1A1A1A] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#4A90E2] focus:border-transparent transition-all duration-300 invalid:border-[#FF3366] invalid:ring-[#FF3366]"
              placeholder="<EMAIL>"
            />
          </div>

          {/* Message Field */}
          <div className="form-field opacity-0">
            <label
              htmlFor="contact-message"
              className="block text-sm font-medium text-gray-300 mb-2"
            >
              Message <span className="text-[#FF3366]">*</span>
            </label>
            <textarea
              id="contact-message"
              name="message"
              value={formData.message}
              onChange={handleInputChange}
              required
              rows={5}
              className="w-full px-4 py-3 bg-[#1A1A1A] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#4A90E2] focus:border-transparent transition-all duration-300 resize-vertical invalid:border-[#FF3366] invalid:ring-[#FF3366]"
              placeholder="Tell me about your project or just say hello..."
            />
          </div>

          {/* Email Me Button */}
          <div className="form-field opacity-0">
            <button
              type="button"
              onClick={handleEmailClick}
              className="w-full inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-[#FF3366] to-[#4A90E2] text-white font-semibold rounded-lg hover:from-[#FF1A4D] hover:to-[#3A7BC8] transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-[#FF3366] focus:ring-offset-2 focus:ring-offset-[#0F0F0F]"
              aria-label="Send me an email with your contact information"
            >
              <span className="mr-2">📧</span>
              Email Me
            </button>
          </div>
        </form>
      </div>
    </section>
  );
}

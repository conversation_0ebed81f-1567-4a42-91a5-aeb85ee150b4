/**
 * Test setup file for Vitest
 */

import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Mock GSAP globally
const mockGSAP = {
  to: vi.fn(),
  from: vi.fn(),
  fromTo: vi.fn(),
  set: vi.fn(),
  timeline: vi.fn(() => ({
    to: vi.fn().mockReturnThis(),
    from: vi.fn().mockReturnThis(),
    fromTo: vi.fn().mockReturnThis(),
    call: vi.fn().mockReturnThis(),
    kill: vi.fn(),
    onComplete: vi.fn(),
  })),
  registerPlugin: vi.fn(),
  killTweensOf: vi.fn(),
};

const mockScrollTrigger = {
  create: vi.fn(),
  refresh: vi.fn(),
  update: vi.fn(),
};

// Mock window.gsap and ScrollTrigger
Object.defineProperty(window, 'gsap', {
  value: mockGSAP,
  writable: true,
});

Object.defineProperty(window, 'ScrollTrigger', {
  value: mockScrollTrigger,
  writable: true,
});

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock performance.memory for performance monitoring
Object.defineProperty(performance, 'memory', {
  value: {
    usedJSHeapSize: 1000000,
    totalJSHeapSize: 2000000,
    jsHeapSizeLimit: 4000000,
  },
  writable: true,
});

// Mock requestAnimationFrame
global.requestAnimationFrame = vi.fn(cb => setTimeout(cb, 16));
global.cancelAnimationFrame = vi.fn(id => clearTimeout(id));

// Suppress console warnings in tests
const originalWarn = console.warn;
console.warn = (...args) => {
  if (
    typeof args[0] === 'string' &&
    (args[0].includes('React Router') || 
     args[0].includes('Warning: ') ||
     args[0].includes('GSAP'))
  ) {
    return;
  }
  originalWarn(...args);
};

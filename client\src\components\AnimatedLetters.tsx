import { useEffect, useRef, useCallback } from "react";
import { useGSAP, useLetterAnimation } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { createEventCleanup, getAccessibilityConfig, randomChoice } from '@/utils';
import {
  ANIMATION_DURATIONS,
  ANIMATION_DELAYS,
  ANIMATION_EASINGS,
  COLOR_VARIANTS,
  LETTER_ANIMATION_TYPES
} from '@/constants';
import type { LetterAnimationConfig } from '@/types';

export default function AnimatedLetters() {
  const containerRef = useRef<HTMLDivElement>(null);
  const lettersInitialized = useRef(false);
  const eventCleanup = useRef(createEventCleanup());
  const { handleError } = useErrorHandler();
  const { gsap, animate, kill, isAvailable } = useGSAP();
  const { animateLetters } = useLetterAnimation();

  const initializeAnimations = useCallback(() => {
    try {
      if (!isAvailable || lettersInitialized.current) return;

      const letters = document.querySelectorAll('.letter');
      if (!letters.length) return;

      lettersInitialized.current = true;

      // Clear any existing animations
      kill();

      // Animate letters in with stagger
      animateLetters(letters, {
        duration: ANIMATION_DURATIONS.SLOW,
        ease: ANIMATION_EASINGS.POWER2_OUT,
        stagger: ANIMATION_DELAYS.SHORT,
      });

      // Setup interactions after animation
      setTimeout(() => {
        setupHoverEffects();
        startContinuousLoop();
      }, 2000);

    } catch (error) {
      handleError(error as Error);
    }
  }, [isAvailable, kill, animateLetters, handleError]);

  useEffect(() => {
    if (isAvailable) {
      initializeAnimations();
    }

    return () => {
      eventCleanup.current.cleanup();
      kill();
    };
  }, [isAvailable, initializeAnimations, kill]);

  const setupHoverEffects = useCallback(() => {
    try {
      if (!isAvailable) return;

      const letters = document.querySelectorAll('.letter');
      const accessibility = getAccessibilityConfig();

      letters.forEach((letter: Element, index: number) => {
        const animationType = LETTER_ANIMATION_TYPES[index % LETTER_ANIMATION_TYPES.length];

        const handleMouseEnter = () => {
          if (accessibility.reduceMotion) return;

          const config = getAnimationConfigForType(animationType);
          animateLetterHover(letter, config);
        };

        const handleMouseLeave = () => {
          if (accessibility.reduceMotion) return;

          resetLetterState(letter);
        };

        const handleClick = () => {
          if (accessibility.reduceMotion) return;

          animateLetterClick(letter);
        };

        // Add event listeners with cleanup tracking
        eventCleanup.current.add(letter, 'mouseenter', handleMouseEnter);
        eventCleanup.current.add(letter, 'mouseleave', handleMouseLeave);
        eventCleanup.current.add(letter, 'click', handleClick);
      });
    } catch (error) {
      handleError(error as Error);
    }
  }, [isAvailable, handleError]);

  const getAnimationConfigForType = (type: string): LetterAnimationConfig => {
    const configs: Record<string, LetterAnimationConfig> = {
      glow: {
        type: 'glow',
        color: COLOR_VARIANTS.PINK,
        scale: 1.2,
        duration: ANIMATION_DURATIONS.FAST,
      },
      rotation: {
        type: 'rotation',
        color: COLOR_VARIANTS.BLUE,
        rotation: 360,
        scale: 1.1,
        duration: ANIMATION_DURATIONS.NORMAL,
      },
      bounce: {
        type: 'bounce',
        color: COLOR_VARIANTS.BLUE,
        scale: 1.15,
        duration: ANIMATION_DURATIONS.FAST,
        ease: ANIMATION_EASINGS.BOUNCE_OUT,
      },
      flip: {
        type: 'flip',
        color: COLOR_VARIANTS.PINK,
        scale: 1.3,
        duration: ANIMATION_DURATIONS.FAST,
      },
      pulse: {
        type: 'pulse',
        scale: 1.15,
        duration: ANIMATION_DURATIONS.FAST,
        repeat: 1,
        yoyo: true,
      },
    };

    return configs[type] || configs.glow;
  };

  const animateLetterHover = useCallback((letter: Element, config: LetterAnimationConfig) => {
    if (!isAvailable) return;

    const { type, color, scale, rotation, duration, ease } = config;

    switch (type) {
      case 'glow':
        animate(letter, {
          scale,
          color,
          textShadow: `0 0 20px ${color}80, 0 0 40px ${color}50`,
          filter: "brightness(1.5)",
          duration,
          ease: ease || ANIMATION_EASINGS.POWER2_OUT,
        });
        break;

      case 'rotation':
        animate(letter, {
          rotation,
          scale,
          color,
          textShadow: `0 0 20px ${color}80`,
          duration,
          ease: ease || ANIMATION_EASINGS.POWER2_IN_OUT,
        });
        break;

      case 'bounce':
        animate(letter, {
          y: -10,
          scale,
          color,
          textShadow: `0 0 30px ${color}80`,
          duration,
          ease: ease || ANIMATION_EASINGS.BOUNCE_OUT,
        });
        break;

      case 'flip':
        animate(letter, {
          rotationY: 180,
          scale,
          color,
          textShadow: `0 0 25px ${color}80`,
          duration,
          ease: ease || ANIMATION_EASINGS.POWER2_OUT,
        });
        break;

      case 'pulse':
        animate(letter, {
          scale,
          textShadow: "0 0 25px currentColor",
          duration,
          repeat: config.repeat,
          yoyo: config.yoyo,
          ease: ease || ANIMATION_EASINGS.POWER2_IN_OUT,
        });
        break;
    }
  }, [isAvailable, animate]);

  const resetLetterState = useCallback((letter: Element) => {
    if (!isAvailable) return;

    animate(letter, {
      scale: 1,
      rotation: 0,
      rotationY: 0,
      y: 0,
      z: 0,
      color: COLOR_VARIANTS.WHITE,
      textShadow: "none",
      filter: "blur(0px) brightness(1) saturate(1)",
      duration: ANIMATION_DURATIONS.NORMAL,
      ease: ANIMATION_EASINGS.POWER2_OUT,
    });
  }, [isAvailable, animate]);

  const animateLetterClick = useCallback((letter: Element) => {
    if (!isAvailable) return;

    // Create a click animation sequence
    const timeline = gsap?.timeline();
    if (!timeline) return;

    timeline
      .to(letter, {
        scale: 0.8,
        rotation: 180,
        filter: "blur(4px) brightness(1.6)",
        duration: ANIMATION_DURATIONS.FAST,
        ease: ANIMATION_EASINGS.POWER2_IN,
      })
      .to(letter, {
        scale: 1.2,
        rotation: 360,
        filter: "blur(1px) saturate(1.5)",
        duration: ANIMATION_DURATIONS.FAST,
        ease: ANIMATION_EASINGS.POWER2_OUT,
      })
      .to(letter, {
        scale: 1,
        rotation: 0,
        filter: "blur(0px) brightness(1) saturate(1)",
        duration: ANIMATION_DURATIONS.NORMAL,
        ease: ANIMATION_EASINGS.BACK_OUT,
      });
  }, [isAvailable, gsap]);

  const startContinuousLoop = useCallback(() => {
    if (!isAvailable) return;

    const letters = document.querySelectorAll('.letter');
    const accessibility = getAccessibilityConfig();

    if (accessibility.reduceMotion || !letters.length) return;

    const timeline = gsap?.timeline({ repeat: -1, repeatDelay: 4 });
    if (!timeline) return;

    letters.forEach((letter: Element, index: number) => {
      const delay = index * 0.05;
      const color = index % 2 === 0 ? COLOR_VARIANTS.PINK : COLOR_VARIANTS.BLUE;

      // Single smooth animation from 0 to 360 degrees
      timeline.to(letter, {
        y: -10,
        rotationY: 360,  // Go directly to 360 instead of stopping at 180
        color,
        duration: ANIMATION_DURATIONS.SLOW * 2,  // Make it longer since it's one animation
        ease: ANIMATION_EASINGS.POWER2_IN_OUT,
        delay,
      }, 0);

      // Return to original position and color
      timeline.to(letter, {
        y: 0,
        rotationY: 0,  // Reset rotation to 0
        color: COLOR_VARIANTS.WHITE,
        duration: ANIMATION_DURATIONS.SLOW,
        ease: ANIMATION_EASINGS.POWER2_IN_OUT,
      }, 2.0 + delay);  // Adjust timing to account for longer first animation
    });
  }, [isAvailable, gsap]);

  return (
    <div
      ref={containerRef}
      className="letter-container"
      style={{ perspective: '1000px' }}
      role="heading"
      aria-level={1}
      aria-label="Jaime Ryan"
    >
      <h1 className="text-4xl md:text-6xl lg:text-8xl xl:text-9xl font-black tracking-wider leading-none text-foreground">
        <div className="mb-4 flex justify-center" role="presentation">
          <span className="letter inline-block" data-letter="J" aria-hidden="true">J</span>
          <span className="letter inline-block" data-letter="A" aria-hidden="true">A</span>
          <span className="letter inline-block" data-letter="I" aria-hidden="true">I</span>
          <span className="letter inline-block" data-letter="M" aria-hidden="true">M</span>
          <span className="letter inline-block" data-letter="E" aria-hidden="true">E</span>
        </div>
        <div className="flex justify-center" role="presentation">
          <span className="letter inline-block" data-letter="R" aria-hidden="true">R</span>
          <span className="letter inline-block" data-letter="Y" aria-hidden="true">Y</span>
          <span className="letter inline-block" data-letter="A" aria-hidden="true">A</span>
          <span className="letter inline-block" data-letter="N" aria-hidden="true">N</span>
        </div>
      </h1>
    </div>
  );
}

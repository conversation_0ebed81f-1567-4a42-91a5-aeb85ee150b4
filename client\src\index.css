@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 180 4% 10%; /* #1A1A1A */
  --foreground: 0 0% 96%; /* #F5F5F5 */
  --muted: 240 5% 64%; /* #A1A1AA */
  --muted-foreground: 240 4% 46%; /* #71717A */
  --popover: 180 4% 10%; /* #1A1A1A */
  --popover-foreground: 0 0% 96%; /* #F5F5F5 */
  --card: 180 4% 10%; /* #1A1A1A */
  --card-foreground: 0 0% 96%; /* #F5F5F5 */
  --border: 240 6% 20%; /* #27272A */
  --input: 240 6% 20%; /* #27272A */
  --primary: 342 89% 60%; /* #FF3366 */
  --primary-foreground: 210 40% 98%; /* #F8FAFC */
  --secondary: 240 5% 84%; /* #D4D4D8 */
  --secondary-foreground: 240 6% 10%; /* #18181B */
  --accent: 210 87% 56%; /* #4A90E2 */
  --accent-foreground: 210 40% 98%; /* #F8FAFC */
  --destructive: 0 84% 60%; /* #EF4444 */
  --destructive-foreground: 0 0% 98%; /* #FAFAFA */
  --ring: 342 89% 60%; /* #FF3366 */
  --radius: 0.5rem;
  --chart-1: 342 89% 60%; /* #FF3366 */
  --chart-2: 210 87% 56%; /* #4A90E2 */
  --chart-3: 120 61% 50%; /* #22C55E */
  --chart-4: 45 93% 58%; /* #F59E0B */
  --chart-5: 271 81% 56%; /* #8B5CF6 */
  --sidebar-background: 180 4% 10%; /* #1A1A1A */
  --sidebar-foreground: 0 0% 96%; /* #F5F5F5 */
  --sidebar-primary: 342 89% 60%; /* #FF3366 */
  --sidebar-primary-foreground: 210 40% 98%; /* #F8FAFC */
  --sidebar-accent: 210 87% 56%; /* #4A90E2 */
  --sidebar-accent-foreground: 210 40% 98%; /* #F8FAFC */
  --sidebar-border: 240 6% 20%; /* #27272A */
  --sidebar-ring: 342 89% 60%; /* #FF3366 */

  /* Z-index layers for proper stacking */
  --z-background: 0;
  --z-parallax-stars: 1;
  --z-particles: 2;
  --z-cursor-trail: 3;
  --z-content: 10;
  --z-navigation: 20;
  --z-ufo: 50;
  --z-modal: 100;
  --z-tooltip: 1000;
}

.dark {
  --background: 180 4% 10%; /* #1A1A1A */
  --foreground: 0 0% 96%; /* #F5F5F5 */
  --muted: 240 5% 16%; /* #27272A */
  --muted-foreground: 240 5% 64%; /* #A1A1AA */
  --popover: 180 4% 10%; /* #1A1A1A */
  --popover-foreground: 0 0% 96%; /* #F5F5F5 */
  --card: 180 4% 10%; /* #1A1A1A */
  --card-foreground: 0 0% 96%; /* #F5F5F5 */
  --border: 240 6% 20%; /* #27272A */
  --input: 240 6% 20%; /* #27272A */
  --primary: 342 89% 60%; /* #FF3366 */
  --primary-foreground: 210 40% 98%; /* #F8FAFC */
  --secondary: 240 5% 16%; /* #27272A */
  --secondary-foreground: 0 0% 96%; /* #F5F5F5 */
  --accent: 210 87% 56%; /* #4A90E2 */
  --accent-foreground: 210 40% 98%; /* #F8FAFC */
  --destructive: 0 63% 31%; /* #7F1D1D */
  --destructive-foreground: 0 0% 98%; /* #FAFAFA */
  --ring: 342 89% 60%; /* #FF3366 */
  --sidebar-background: 180 4% 10%; /* #1A1A1A */
  --sidebar-foreground: 0 0% 96%; /* #F5F5F5 */
  --sidebar-primary: 342 89% 60%; /* #FF3366 */
  --sidebar-primary-foreground: 210 40% 98%; /* #F8FAFC */
  --sidebar-accent: 210 87% 56%; /* #4A90E2 */
  --sidebar-accent-foreground: 210 40% 98%; /* #F8FAFC */
  --sidebar-border: 240 6% 20%; /* #27272A */
  --sidebar-ring: 342 89% 60%; /* #FF3366 */
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Montserrat', sans-serif;
    overflow-x: hidden;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer utilities {
  .font-montserrat {
    font-family: 'Montserrat', sans-serif;
  }
  
  .letter {
    display: inline-block;
    opacity: 0;
    transform: translateY(100px) rotateX(90deg);
    transform-origin: center bottom;
    will-change: transform, opacity, filter;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    filter: blur(0px);
  }
  
  .letter-blur {
    filter: blur(2px) brightness(1.2);
  }
  
  .letter-depth {
    transform-style: preserve-3d;
    filter: blur(1px) saturate(1.3);
  }
  
  .particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: hsl(var(--accent));
    border-radius: 50%;
    opacity: 0.6;
    pointer-events: none;
  }
  
  .glow-pink {
    text-shadow: 0 0 20px rgba(255, 51, 102, 0.5), 0 0 40px rgba(255, 51, 102, 0.3);
  }
  
  .glow-blue {
    text-shadow: 0 0 20px rgba(74, 144, 226, 0.5), 0 0 40px rgba(74, 144, 226, 0.3);
  }
  
  .bg-gradient-radial {
    background: radial-gradient(circle at center, rgba(74, 144, 226, 0.1) 0%, #1A1A1A 70%);
  }

  /* Space effects utilities */
  .parallax-star {
    position: absolute;
    border-radius: 50%;
    pointer-events: none;
    will-change: transform;
    transition: opacity 0.3s ease;
  }

  .cursor-trail-particle {
    position: fixed;
    border-radius: 50%;
    pointer-events: none;
    will-change: transform, opacity, scale;
    z-index: var(--z-cursor-trail);
  }

  /* Performance optimizations */
  .gpu-accelerated {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
  }

  /* Reduced motion support */
  @media (prefers-reduced-motion: reduce) {
    .parallax-star,
    .cursor-trail-particle {
      animation: none !important;
      transform: none !important;
    }

    .letter {
      transform: none !important;
      opacity: 1 !important;
    }
  }
}

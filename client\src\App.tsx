import { Switch, Route } from "wouter";
import { QueryClientProvider } from "@tanstack/react-query";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Toaster } from "@/components/ui/toaster";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { PerformanceMonitor } from "@/components/PerformanceMonitor";
import { queryClient } from "@/lib/queryClient";
import Home from "@/pages/home";
import NotFound from "@/pages/not-found";
import SpaceEffectsTest from "@/components/SpaceEffectsTest";

function Router() {
  return (
    <Switch>
      <Route path="/" component={Home} />
      <Route path="/test" component={SpaceEffectsTest} />
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  const handleError = (error: Error, errorInfo: any) => {
    // Log error to monitoring service in production
    console.error('Application Error:', error, errorInfo);

    // In production, you might want to send this to an error tracking service
    // like Sentry, LogRocket, or Bugsnag
  };

  return (
    <ErrorBoundary onError={handleError}>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Router />
          <Toaster />
          <PerformanceMonitor />
        </TooltipProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;

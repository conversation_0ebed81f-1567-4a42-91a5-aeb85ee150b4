import { useEffect, useRef } from "react";
import { useGSAP } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';

export default function UFOAnimation() {
  const ufoRef = useRef<HTMLDivElement>(null);
  const beamRef = useRef<HTMLDivElement>(null);
  const animationLoopRef = useRef<NodeJS.Timeout | null>(null);
  const isAnimationActive = useRef(false);
  const ufoDirection = useRef<'left-to-right' | 'right-to-left'>('left-to-right');
  const { gsap, isAvailable } = useGSAP();
  const { handleError } = useErrorHandler();

  useEffect(() => {
    if (isAvailable && !isAnimationActive.current) {
      const accessibility = getAccessibilityConfig();

      // Skip UFO animation for reduced motion
      if (accessibility.reduceMotion) return;

      // Start continuous UFO animation loop after page loads
      setTimeout(() => {
        if (!isAnimationActive.current) {
          isAnimationActive.current = true;
          startContinuousUFOLoop();
        }
      }, 2000); // Wait 2 seconds after page load
    }

    // Cleanup on unmount
    return () => {
      if (animationLoopRef.current) {
        clearTimeout(animationLoopRef.current);
        animationLoopRef.current = null;
      }
      isAnimationActive.current = false;
    };
  }, [isAvailable]);

  const startContinuousUFOLoop = () => {
    if (!isAnimationActive.current) return;

    // Start the first UFO sequence
    startUFOSequence(ufoDirection.current);

    // Schedule the next UFO after the current sequence completes
    // Total sequence duration: 3s (fly in) + 1.5s (hover/beam) + 2s (abduct) + 3s (fly out) + 5s (letter recovery) = ~14.5s
    // Adding some buffer time for smooth transitions
    const nextUFODelay = 18000; // 18 seconds between UFO appearances

    animationLoopRef.current = setTimeout(() => {
      // Alternate direction for next UFO
      ufoDirection.current = ufoDirection.current === 'left-to-right' ? 'right-to-left' : 'left-to-right';
      
      // Continue the loop
      startContinuousUFOLoop();
    }, nextUFODelay);
  };

  const startUFOSequence = (direction: 'left-to-right' | 'right-to-left') => {
    try {
      if (!ufoRef.current || !beamRef.current || !gsap || !isAnimationActive.current) return;

      const letters = document.querySelectorAll('.letter');
      if (letters.length === 0) return;

      // Pick a random letter to abduct
      const randomIndex = Math.floor(Math.random() * letters.length);
      const targetLetter = letters[randomIndex] as HTMLElement;
      const originalLetter = targetLetter.textContent;

      // Get target letter position - using viewport coordinates since UFO is fixed positioned
      const letterRect = targetLetter.getBoundingClientRect();
      const letterX = letterRect.left + letterRect.width / 2;
      const letterY = letterRect.top + letterRect.height / 2;

      const timeline = gsap.timeline();

      // Determine entry and exit positions based on direction
      const isLeftToRight = direction === 'left-to-right';
      const entryX = isLeftToRight ? -200 : window.innerWidth + 200;
      const exitX = isLeftToRight ? window.innerWidth + 200 : -200;
      const entryRotation = isLeftToRight ? -10 : 10;
      const exitRotation = isLeftToRight ? 15 : -15;

      // Phase 1: UFO flies in from entry side
      timeline.set(ufoRef.current, {
        left: entryX,
        top: letterY - 150,
        opacity: 1,
        rotation: entryRotation,
        scaleX: isLeftToRight ? 1 : -1 // Flip UFO when coming from right
      });

      timeline.to(ufoRef.current, {
        left: letterX - 32, // Center UFO over letter (UFO width is 64px)
        rotation: 0,
        duration: 3,
        ease: "power2.inOut"
      });

      // Phase 2: UFO hovers and activates beam
      timeline.to(ufoRef.current, {
        top: letterY - 120,
        duration: 0.5,
        ease: "power2.out"
      });

      // Show and animate the beam (positioned relative to UFO)
      timeline.set(beamRef.current, {
        opacity: 1,
        scaleY: 0,
        transformOrigin: "top center"
      });

      timeline.to(beamRef.current, {
        scaleY: 1,
        duration: 1,
        ease: "power2.out"
      });

      // Phase 3: Abduct the letter
      timeline.to(targetLetter, {
        y: -200,
        opacity: 0,
        scale: 0.5,
        rotation: 360,
        filter: "blur(2px) brightness(2)",
        duration: 2,
        ease: "power2.in"
      }, "-=0.5");

      // Phase 4: Hide beam and letter transformation
      timeline.to(beamRef.current, {
        opacity: 0,
        scaleY: 0,
        duration: 0.5,
        ease: "power2.in"
      });

      // Replace with alien glyph
      timeline.call(() => {
        replaceWithAlienGlyph(targetLetter, originalLetter);
      });

      // Phase 5: UFO flies away to exit side
      timeline.to(ufoRef.current, {
        left: exitX,
        top: letterY - 200,
        rotation: exitRotation,
        duration: 3,
        ease: "power2.in"
      });

      timeline.to(ufoRef.current, {
        opacity: 0,
        duration: 0.5
      });

    } catch (error) {
      handleError(error as Error);
    }
  };

  const replaceWithAlienGlyph = (targetLetter: HTMLElement, originalLetter: string | null) => {
    if (!gsap) return;

    const alienGlyphs = ['◊', '◈', '◇', '◉', '⬢', '⬡', '⟐', '⟡'];
    const randomGlyph = alienGlyphs[Math.floor(Math.random() * alienGlyphs.length)];

    // Reset letter position and apply alien styling
    gsap.set(targetLetter, {
      y: 0,
      scale: 1,
      rotation: 0,
      filter: "blur(0px) brightness(1)"
    });

    targetLetter.textContent = randomGlyph;
    targetLetter.style.color = '#4A90E2';
    targetLetter.style.textShadow = '0 0 20px rgba(74, 144, 226, 0.8)';

    // Glitch in animation
    const glitchTimeline = gsap.timeline({
      onComplete: () => {
        // Start glitch return after 3 seconds
        setTimeout(() => {
          glitchReturnToOriginal(targetLetter, originalLetter);
        }, 3000);
      }
    });
    
    glitchTimeline.fromTo(targetLetter, 
      { opacity: 0, scale: 0, filter: "blur(5px)" },
      { 
        opacity: 1, 
        scale: 1.2, 
        filter: "blur(0px)",
        duration: 0.3,
        ease: "back.out(1.7)"
      }
    );

    glitchTimeline.to(targetLetter, {
      scale: 1,
      duration: 0.2,
      ease: "power2.out"
    });

    // Flicker effect
    for (let i = 0; i < 3; i++) {
      glitchTimeline.to(targetLetter, {
        opacity: 0.3,
        duration: 0.05,
        yoyo: true,
        repeat: 1
      });
    }
  };

  const glitchReturnToOriginal = (targetLetter: HTMLElement, originalLetter: string | null) => {
    if (!originalLetter || !gsap) return;

    const explosionTimeline = gsap.timeline();
    const letterRect = targetLetter.getBoundingClientRect();
    const particles: HTMLElement[] = [];

    // Create particle explosion
    explosionTimeline.call(() => {
      createParticleExplosion(targetLetter, letterRect, particles);
    });

    // Hide the glyph during explosion
    explosionTimeline.to(targetLetter, {
      opacity: 0,
      scale: 1.5,
      filter: "blur(5px) brightness(2)",
      duration: 0.2,
      ease: "power2.out"
    });

    // Animate particles
    explosionTimeline.call(() => {
      animateParticles(particles);
    });

    // Wait for particle animation, then restore letter
    explosionTimeline.to({}, { duration: 1.5 }); // Wait for particles

    explosionTimeline.call(() => {
      // Clean up particles
      particles.forEach(particle => particle.remove());
      
      // Restore original letter and styling
      targetLetter.textContent = originalLetter;
      targetLetter.style.color = '#F5F5F5';
      targetLetter.style.textShadow = 'none';
    });

    // Letter rematerialization
    explosionTimeline.fromTo(targetLetter,
      { opacity: 0, scale: 0.3, filter: "blur(8px)", rotation: 180 },
      {
        opacity: 1,
        scale: 1,
        rotation: 0,
        filter: "blur(0px) brightness(1)",
        duration: 0.8,
        ease: "back.out(1.7)"
      }
    );

    // Final energy pulse
    explosionTimeline.to(targetLetter, {
      textShadow: "0 0 20px rgba(255, 255, 255, 0.8), 0 0 40px rgba(74, 144, 226, 0.4)",
      duration: 0.3,
      ease: "power2.out"
    });

    explosionTimeline.to(targetLetter, {
      textShadow: "none",
      duration: 0.5,
      ease: "power2.out"
    });
  };

  const createParticleExplosion = (targetLetter: HTMLElement, letterRect: DOMRect, particles: HTMLElement[]) => {
    const particleCount = 12;
    const centerX = letterRect.left + letterRect.width / 2;
    const centerY = letterRect.top + letterRect.height / 2;

    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'explosion-particle';
      
      // Random particle styling
      const size = 4 + Math.random() * 8;
      const hue = 180 + Math.random() * 60; // Blue to cyan range
      
      particle.style.cssText = `
        position: fixed;
        width: ${size}px;
        height: ${size}px;
        background: hsl(${hue}, 80%, 60%);
        border-radius: 50%;
        left: ${centerX}px;
        top: ${centerY}px;
        pointer-events: none;
        z-index: 100;
        box-shadow: 0 0 10px hsl(${hue}, 80%, 60%);
      `;
      
      document.body.appendChild(particle);
      particles.push(particle);
    }
  };

  const animateParticles = (particles: HTMLElement[]) => {
    if (!gsap) return;

    particles.forEach((particle, index) => {
      const angle = (index / particles.length) * Math.PI * 2;
      const velocity = 100 + Math.random() * 150;
      const xOffset = Math.cos(angle) * velocity;
      const yOffset = Math.sin(angle) * velocity;

      gsap.to(particle, {
        x: xOffset,
        y: yOffset,
        opacity: 0,
        scale: 0.2,
        rotation: 360 + Math.random() * 720,
        duration: 1.2,
        ease: "power2.out"
      });
    });
  };

  return (
    <>
      {/* UFO Wrapper */}
      <div
        ref={ufoRef}
        className="absolute pointer-events-none z-50 ufo-wrapper"
        style={{ opacity: 0 }}
      >
        <div className="relative">
          {/* UFO Body */}
          <div className="w-16 h-8 bg-gradient-to-b from-gray-300 to-gray-600 rounded-full relative shadow-lg">
            {/* UFO Dome */}
            <div className="absolute -top-2 left-1/2 transform -translate-x-1/2 w-10 h-6 bg-gradient-to-b from-blue-200 to-blue-400 rounded-full opacity-80"></div>
            
            {/* UFO Lights */}
            <div className="absolute bottom-1 left-2 w-1 h-1 bg-yellow-400 rounded-full animate-pulse"></div>
            <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-red-400 rounded-full animate-pulse" style={{ animationDelay: '0.3s' }}></div>
            <div className="absolute bottom-1 right-2 w-1 h-1 bg-green-400 rounded-full animate-pulse" style={{ animationDelay: '0.6s' }}></div>
          </div>
          
          {/* Abduction Beam - positioned relative to UFO */}
          <div
            ref={beamRef}
            className="absolute pointer-events-none z-40"
            style={{ 
              opacity: 0,
              top: '100%',
              left: '50%',
              transform: 'translateX(-50%)'
            }}
          >
            <div 
              className="w-20 h-32 bg-gradient-to-b from-cyan-400 to-transparent opacity-60"
              style={{
                clipPath: 'polygon(40% 0%, 60% 0%, 100% 100%, 0% 100%)',
                filter: 'blur(1px)'
              }}
            ></div>
          </div>
        </div>
      </div>
    </>
  );
}
/**
 * Custom hooks for GSAP animations with proper cleanup and error handling
 */

import { useEffect, useRef, useCallback } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import type { GSAPInstance, GSAPTimeline, AnimationConfig } from '@/types';
import { isGSAPAvailable, safeGSAPCall, getAccessibilityConfig } from '@/utils';
import { ANIMATION_DURATIONS, ANIMATION_EASINGS } from '@/constants';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

/**
 * Hook for basic GSAP animations with cleanup
 */
export function useGSAP() {
  const timelineRef = useRef<GSAPTimeline | null>(null);
  const tweensRef = useRef<any[]>([]);
  const isAvailable = isGSAPAvailable();

  const createTimeline = useCallback((config?: any): any => {
    if (!isAvailable) return null;

    const timeline = gsap.timeline(config);
    timelineRef.current = timeline as any;
    return timeline;
  }, [isAvailable]);

  const animate = useCallback((
    target: any,
    vars: any,
    config?: AnimationConfig
  ): any => {
    if (!isAvailable) return null;

    const accessibility = getAccessibilityConfig();

    // Respect reduced motion preference
    if (accessibility.reduceMotion) {
      const reducedVars = {
        ...vars,
        duration: 0.1,
        ease: 'none',
      };
      const tween = gsap.set(target, reducedVars);
      tweensRef.current.push(tween);
      return tween;
    }

    const animationVars = {
      duration: ANIMATION_DURATIONS.NORMAL,
      ease: ANIMATION_EASINGS.POWER2_OUT,
      ...config,
      ...vars,
    };

    const tween = gsap.to(target, animationVars);
    tweensRef.current.push(tween);
    return tween;
  }, [isAvailable]);

  const animateFrom = useCallback((
    target: any,
    vars: any,
    config?: AnimationConfig
  ): any => {
    if (!isAvailable) return null;

    const accessibility = getAccessibilityConfig();

    if (accessibility.reduceMotion) {
      const tween = gsap.set(target, vars);
      tweensRef.current.push(tween);
      return tween;
    }

    const animationVars = {
      duration: ANIMATION_DURATIONS.NORMAL,
      ease: ANIMATION_EASINGS.POWER2_OUT,
      ...config,
      ...vars,
    };

    const tween = gsap.from(target, animationVars);
    tweensRef.current.push(tween);
    return tween;
  }, [isAvailable]);

  const kill = useCallback((target?: any) => {
    if (!isAvailable) return;

    if (target) {
      gsap.killTweensOf(target);
    } else {
      // Kill all tracked tweens
      tweensRef.current.forEach(tween => {
        if (tween && tween.kill) {
          tween.kill();
        }
      });
      tweensRef.current = [];

      if (timelineRef.current) {
        (timelineRef.current as any).kill?.();
        timelineRef.current = null;
      }
    }
  }, [isAvailable]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      kill();
    };
  }, [kill]);

  return {
    gsap,
    timeline: timelineRef.current,
    createTimeline,
    animate,
    animateFrom,
    kill,
    isAvailable,
  };
}

/**
 * Hook for scroll-triggered animations
 */
export function useScrollTrigger() {
  const triggersRef = useRef<any[]>([]);
  const isAvailable = isGSAPAvailable();

  const createScrollTrigger = useCallback((config: any) => {
    if (!isAvailable || !ScrollTrigger) return null;

    const accessibility = getAccessibilityConfig();

    // Disable scroll triggers for reduced motion
    if (accessibility.reduceMotion) {
      return null;
    }

    const trigger = ScrollTrigger.create(config);
    triggersRef.current.push(trigger);
    return trigger;
  }, [isAvailable]);

  const refresh = useCallback(() => {
    if (ScrollTrigger) {
      ScrollTrigger.refresh();
    }
  }, []);

  const cleanup = useCallback(() => {
    triggersRef.current.forEach(trigger => {
      if (trigger && trigger.kill) {
        trigger.kill();
      }
    });
    triggersRef.current = [];
  }, []);

  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    createScrollTrigger,
    refresh,
    cleanup,
    ScrollTrigger,
  };
}

/**
 * Hook for letter animations with performance optimization
 */
export function useLetterAnimation() {
  const { animate, kill, isAvailable } = useGSAP();
  const animationRef = useRef<any>(null);

  const animateLetters = useCallback((
    letters: NodeListOf<Element> | Element[],
    config: AnimationConfig & { stagger?: number } = {}
  ) => {
    if (!isAvailable || !letters.length) return;

    const accessibility = getAccessibilityConfig();

    if (accessibility.reduceMotion) {
      // Just set final state for reduced motion
      Array.from(letters).forEach(letter => {
        gsap.set(letter, {
          opacity: 1,
          y: 0,
          rotation: 0,
          scale: 1,
        });
      });
      return;
    }

    const {
      duration = ANIMATION_DURATIONS.NORMAL,
      delay = 0,
      ease = ANIMATION_EASINGS.POWER2_OUT,
      stagger = 0.1,
      ...vars
    } = config;

    animationRef.current = gsap.fromTo(letters,
      {
        opacity: 0,
        y: 100,
        rotationX: 90,
        transformOrigin: 'center bottom',
      },
      {
        opacity: 1,
        y: 0,
        rotationX: 0,
        duration,
        ease,
        delay,
        stagger,
        ...vars,
      }
    );

    return animationRef.current;
  }, [animate, isAvailable]);

  const stopAnimation = useCallback(() => {
    if (animationRef.current) {
      animationRef.current.kill();
      animationRef.current = null;
    }
  }, []);

  useEffect(() => {
    return stopAnimation;
  }, [stopAnimation]);

  return {
    animateLetters,
    stopAnimation,
  };
}

/**
 * Hook for particle animations with performance monitoring
 */
export function useParticleAnimation() {
  const { animate, kill, isAvailable } = useGSAP();
  const particlesRef = useRef<HTMLElement[]>([]);
  const containerRef = useRef<HTMLElement | null>(null);

  const createParticles = useCallback((
    container: HTMLElement,
    count: number = 50
  ) => {
    if (!isAvailable || !container) return;

    containerRef.current = container;

    // Clear existing particles
    particlesRef.current.forEach(particle => {
      particle.remove();
    });
    particlesRef.current = [];

    const accessibility = getAccessibilityConfig();

    // Reduce particle count for reduced motion
    const particleCount = accessibility.reduceMotion ? Math.min(count, 10) : count;

    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'particle';
      particle.style.cssText = `
        position: absolute;
        width: 2px;
        height: 2px;
        background: #3b82f6;
        border-radius: 50%;
        opacity: 0.6;
        pointer-events: none;
        left: ${Math.random() * 100}%;
        top: ${Math.random() * 100}%;
      `;

      container.appendChild(particle);
      particlesRef.current.push(particle);

      // Animate particle
      if (!accessibility.reduceMotion) {
        animate(particle, {
          y: -window.innerHeight - 100,
          duration: Math.random() * 20 + 10,
          repeat: -1,
          ease: 'none',
          delay: Math.random() * 20,
        });

        animate(particle, {
          opacity: Math.random() * 0.5 + 0.3,
          duration: Math.random() * 3 + 2,
          repeat: -1,
          yoyo: true,
          ease: 'power2.inOut',
        });

        animate(particle, {
          x: Math.random() * 40 - 20,
          duration: Math.random() * 5 + 3,
          repeat: -1,
          yoyo: true,
          ease: 'power1.inOut',
        });
      }
    }
  }, [animate, isAvailable]);

  const cleanup = useCallback(() => {
    particlesRef.current.forEach(particle => {
      kill(particle);
      particle.remove();
    });
    particlesRef.current = [];
  }, [kill]);

  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    createParticles,
    cleanup,
    particleCount: particlesRef.current.length,
  };
}

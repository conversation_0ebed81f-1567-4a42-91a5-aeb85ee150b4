/**
 * Custom hook for mouse-driven parallax effects with performance optimization
 */

import { useEffect, useRef, useCallback } from 'react';
import { gsap } from 'gsap';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import { MOUSE_EFFECTS_CONFIG } from '@/constants';

interface ParallaxLayer {
  elements: HTMLElement[];
  intensity: number; // 0-1, how much the layer moves
  quickSetters: {
    x: Function;
    y: Function;
  }[];
}

interface MousePosition {
  x: number;
  y: number;
  normalizedX: number; // -1 to 1
  normalizedY: number; // -1 to 1
}

export function useMouseParallax() {
  const layersRef = useRef<ParallaxLayer[]>([]);
  const mouseRef = useRef<MousePosition>({ x: 0, y: 0, normalizedX: 0, normalizedY: 0 });
  const rafRef = useRef<number>();
  const isActiveRef = useRef(false);
  const { handleError } = useErrorHandler();

  const updateParallax = useCallback(() => {
    try {
      if (!isActiveRef.current) return;

      const { normalizedX, normalizedY } = mouseRef.current;
      const { MAX_MOVEMENT, SMOOTHING } = MOUSE_EFFECTS_CONFIG.PARALLAX;

      layersRef.current.forEach(layer => {
        const moveX = normalizedX * MAX_MOVEMENT * layer.intensity;
        const moveY = normalizedY * MAX_MOVEMENT * layer.intensity;

        layer.quickSetters.forEach(setter => {
          setter.x(moveX);
          setter.y(moveY);
        });
      });

      rafRef.current = requestAnimationFrame(updateParallax);
    } catch (error) {
      handleError(error as Error);
    }
  }, [handleError]);

  const handleMouseMove = useCallback((event: MouseEvent) => {
    try {
      const { clientX, clientY } = event;
      const { innerWidth, innerHeight } = window;

      // Update mouse position
      mouseRef.current = {
        x: clientX,
        y: clientY,
        normalizedX: (clientX / innerWidth) * 2 - 1, // -1 to 1
        normalizedY: (clientY / innerHeight) * 2 - 1, // -1 to 1
      };

      // Start animation loop if not already running
      if (!rafRef.current && isActiveRef.current) {
        rafRef.current = requestAnimationFrame(updateParallax);
      }
    } catch (error) {
      handleError(error as Error);
    }
  }, [updateParallax, handleError]);

  const throttledMouseMove = useCallback(
    (() => {
      let lastCall = 0;
      return (event: MouseEvent) => {
        const now = Date.now();
        if (now - lastCall >= MOUSE_EFFECTS_CONFIG.PARALLAX.THROTTLE_MS) {
          lastCall = now;
          handleMouseMove(event);
        }
      };
    })(),
    [handleMouseMove]
  );

  const registerLayer = useCallback((
    elements: HTMLElement[],
    intensity: number = 1
  ): number => {
    try {
      const accessibility = getAccessibilityConfig();
      
      // Don't register layers if reduced motion is preferred
      if (accessibility.reduceMotion) {
        return -1;
      }

      const quickSetters = elements.map(element => ({
        x: gsap.quickSetter(element, 'x', 'px'),
        y: gsap.quickSetter(element, 'y', 'px'),
      }));

      const layer: ParallaxLayer = {
        elements,
        intensity: Math.max(0, Math.min(1, intensity)),
        quickSetters,
      };

      layersRef.current.push(layer);
      return layersRef.current.length - 1;
    } catch (error) {
      handleError(error as Error);
      return -1;
    }
  }, [handleError]);

  const unregisterLayer = useCallback((layerIndex: number) => {
    try {
      if (layerIndex >= 0 && layerIndex < layersRef.current.length) {
        layersRef.current.splice(layerIndex, 1);
      }
    } catch (error) {
      handleError(error as Error);
    }
  }, [handleError]);

  const start = useCallback(() => {
    try {
      const accessibility = getAccessibilityConfig();
      
      if (accessibility.reduceMotion) {
        return;
      }

      isActiveRef.current = true;
      window.addEventListener('mousemove', throttledMouseMove, { passive: true });
    } catch (error) {
      handleError(error as Error);
    }
  }, [throttledMouseMove, handleError]);

  const stop = useCallback(() => {
    try {
      isActiveRef.current = false;
      window.removeEventListener('mousemove', throttledMouseMove);
      
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
        rafRef.current = undefined;
      }

      // Reset all elements to center position
      layersRef.current.forEach(layer => {
        layer.quickSetters.forEach(setter => {
          setter.x(0);
          setter.y(0);
        });
      });
    } catch (error) {
      handleError(error as Error);
    }
  }, [throttledMouseMove, handleError]);

  const cleanup = useCallback(() => {
    stop();
    layersRef.current = [];
  }, [stop]);

  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    registerLayer,
    unregisterLayer,
    start,
    stop,
    cleanup,
    isActive: isActiveRef.current,
    mousePosition: mouseRef.current,
  };
}

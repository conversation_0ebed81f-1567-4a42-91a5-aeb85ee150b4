import React, { useRef, useEffect, useCallback } from 'react';
import { useGSAP } from '@/hooks/useGSAP';
import { ANIMATION_DURATIONS, ANIMATION_EASINGS, COLOR_VARIANTS } from '@/constants';
import { getAccessibilityConfig } from '@/utils';

interface EnhancedExploreTextProps {
  className?: string;
}

export const EnhancedExploreText: React.FC<EnhancedExploreTextProps> = ({ 
  className = "" 
}) => {
  const textRef = useRef<HTMLParagraphElement>(null);
  const { gsap, animate, kill, isAvailable } = useGSAP();

  const initializeAnimations = useCallback(() => {
    if (!isAvailable || !gsap || !textRef.current) return;

    const accessibility = getAccessibilityConfig();
    
    // If reduced motion is preferred, apply static enhanced styling
    if (accessibility.reduceMotion) {
      gsap.set(textRef.current, {
        color: COLOR_VARIANTS.PINK,
        scale: 1
      });
      return;
    }

    try {
      // Color rotation arrays with proper timing
      const themeColors = [COLOR_VARIANTS.PINK, COLOR_VARIANTS.BLUE, COLOR_VARIANTS.WHITE];
      const colorDuration = ANIMATION_DURATIONS.VERY_SLOW * 1.5; // 3 seconds per color
      
      // Pulsing animation for text (slower, more subtle)
      const pulseTimeline = gsap.timeline({ repeat: -1, yoyo: true });
      pulseTimeline.to(textRef.current, {
        scale: 1.05,
        duration: ANIMATION_DURATIONS.VERY_SLOW, // Slower pulse
        ease: ANIMATION_EASINGS.POWER2_IN_OUT,
      });

      // Master color rotation timeline for text
      const masterColorTimeline = gsap.timeline({ repeat: -1 });
      
      themeColors.forEach((color, index) => {
        const startTime = index * colorDuration;
        
        // Animate text color
        masterColorTimeline.to(textRef.current, {
          color: color,
          duration: colorDuration * 0.8, // Slightly faster transition
          ease: ANIMATION_EASINGS.POWER2_IN_OUT,
        }, startTime);
      });

    } catch (error) {
      console.error('Error initializing explore text animations:', error);
    }
  }, [isAvailable, gsap, animate]);

  useEffect(() => {
    if (isAvailable) {
      // Small delay to ensure DOM is ready
      const timer = setTimeout(initializeAnimations, 100);
      return () => clearTimeout(timer);
    }
  }, [isAvailable, initializeAnimations]);

  // Cleanup animations on unmount
  useEffect(() => {
    return () => {
      kill();
    };
  }, [kill]);

  return (
    <div className={`relative inline-block ${className}`}>
      <p 
        ref={textRef}
        className="text-xs font-light tracking-widest text-muted-foreground uppercase transform rotate-90 origin-center"
        style={{ willChange: 'transform, color' }}
      >
        Explore
      </p>
    </div>
  );
};

export default EnhancedExploreText; 
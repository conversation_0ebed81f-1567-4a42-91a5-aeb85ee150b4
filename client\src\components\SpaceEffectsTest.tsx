/**
 * Test component for Space Mouse Effects
 */

import { useEffect, useState } from 'react';
import SpaceMouseEffects from './SpaceMouseEffects';

export default function SpaceEffectsTest() {
  const [parallaxEnabled, setParallaxEnabled] = useState(true);
  const [cursorTrailEnabled, setCursorTrailEnabled] = useState(true);

  useEffect(() => {
    console.log('SpaceEffectsTest: Component mounted');
  }, []);

  return (
    <div className="min-h-screen bg-background text-foreground relative overflow-hidden">
      {/* Background gradient */}
      <div className="fixed inset-0 bg-gradient-radial" />
      
      {/* Space Mouse Effects */}
      <SpaceMouseEffects 
        enableParallax={parallaxEnabled}
        enableCursorTrail={cursorTrailEnabled}
      />
      
      {/* Test Content */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen p-8">
        <h1 className="text-4xl font-bold mb-8 text-center">
          Space Mouse Effects Test
        </h1>
        
        <div className="bg-card p-6 rounded-lg shadow-lg max-w-md w-full">
          <h2 className="text-xl font-semibold mb-4">Controls</h2>
          
          <div className="space-y-4">
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={parallaxEnabled}
                onChange={(e) => setParallaxEnabled(e.target.checked)}
                className="w-4 h-4"
              />
              <span>Enable Parallax Stars</span>
            </label>
            
            <label className="flex items-center space-x-3">
              <input
                type="checkbox"
                checked={cursorTrailEnabled}
                onChange={(e) => setCursorTrailEnabled(e.target.checked)}
                className="w-4 h-4"
              />
              <span>Enable Cursor Trail</span>
            </label>
          </div>
          
          <div className="mt-6 text-sm text-muted-foreground">
            <p>Move your mouse around to see the effects!</p>
            <p className="mt-2">
              • Parallax stars should move subtly with mouse movement
            </p>
            <p>
              • Cursor trail should create glowing particles that follow your cursor
            </p>
          </div>
        </div>
        
        {/* Large text area for testing */}
        <div className="mt-12 max-w-2xl text-center">
          <h3 className="text-2xl font-semibold mb-4">Test Text Readability</h3>
          <p className="text-lg leading-relaxed">
            This text should remain perfectly readable while the space effects are active. 
            The parallax stars should create a subtle 3D depth effect without interfering 
            with text legibility. The cursor trail should add a magical touch without 
            being overwhelming or distracting from the content.
          </p>
        </div>
      </div>
    </div>
  );
}

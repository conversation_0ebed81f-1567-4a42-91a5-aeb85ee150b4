/**
 * UFO-style spaceship SVG component (static decorative version)
 */

import { useRef } from 'react';
import { COLOR_VARIANTS } from '@/constants';

interface UFOSpaceshipProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  onHover?: (isHovering: boolean) => void;
}

export default function UFOSpaceship({ 
  className = '', 
  size = 'md',
  onHover 
}: UFOSpaceshipProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const ufoRef = useRef<SVGSVGElement>(null);
  const beamRef = useRef<SVGGElement>(null);
  const glowRef = useRef<SVGGElement>(null);
  const lightsRef = useRef<SVGGElement>(null);

  // Size configurations
  const sizeConfig = {
    sm: { width: 56, height: 48, viewBox: '0 0 56 48' },
    md: { width: 72, height: 60, viewBox: '0 0 72 60' },
    lg: { width: 88, height: 72, viewBox: '0 0 88 72' }
  };

  const currentSize = sizeConfig[size];

  return (
    <div
      ref={containerRef}
      className={`inline-block ${className}`}
      style={{ filter: 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3))' }}
    >
      <svg
        ref={ufoRef}
        width={currentSize.width}
        height={currentSize.height}
        viewBox={currentSize.viewBox}
        className="overflow-visible"
        style={{ transformOrigin: 'center center' }}
      >
        {/* Glow Effect */}
        <defs>
          <radialGradient id="ufo-glow" cx="50%" cy="50%" r="70%">
            <stop offset="0%" stopColor={COLOR_VARIANTS.BLUE} stopOpacity="0.6" />
            <stop offset="50%" stopColor={COLOR_VARIANTS.PINK} stopOpacity="0.3" />
            <stop offset="100%" stopColor="transparent" stopOpacity="0" />
          </radialGradient>
          
          <linearGradient id="ufo-body" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor="#E8E8E8" />
            <stop offset="50%" stopColor={COLOR_VARIANTS.WHITE} />
            <stop offset="100%" stopColor="#D0D0D0" />
          </linearGradient>
          
          <linearGradient id="ufo-dome" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor={COLOR_VARIANTS.BLUE} stopOpacity="0.8" />
            <stop offset="50%" stopColor={COLOR_VARIANTS.BLUE} stopOpacity="0.4" />
            <stop offset="100%" stopColor={COLOR_VARIANTS.BLUE} stopOpacity="0.2" />
          </linearGradient>
          
          <linearGradient id="tractor-beam" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor={COLOR_VARIANTS.BLUE} stopOpacity="0.6" />
            <stop offset="50%" stopColor={COLOR_VARIANTS.BLUE} stopOpacity="0.3" />
            <stop offset="100%" stopColor={COLOR_VARIANTS.BLUE} stopOpacity="0.1" />
          </linearGradient>
        </defs>

        {/* Glow Background */}
        <g ref={glowRef} opacity="0.4">
          <ellipse
            cx={currentSize.width / 2}
            cy={currentSize.height * 0.4}
            rx={currentSize.width * 0.6}
            ry={currentSize.height * 0.3}
            fill="url(#ufo-glow)"
          />
        </g>

        {/* Tractor Beam */}
        <g ref={beamRef} opacity="0.3" style={{ transformOrigin: `${currentSize.width / 2}px ${currentSize.height * 0.5}px` }}>
          <polygon
            points={`${currentSize.width * 0.4},${currentSize.height * 0.5} ${currentSize.width * 0.6},${currentSize.height * 0.5} ${currentSize.width * 0.8},${currentSize.height * 0.9} ${currentSize.width * 0.2},${currentSize.height * 0.9}`}
            fill="url(#tractor-beam)"
          />
        </g>

        {/* UFO Body */}
        <g>
          {/* Main Saucer */}
          <ellipse
            cx={currentSize.width / 2}
            cy={currentSize.height * 0.45}
            rx={currentSize.width * 0.35}
            ry={currentSize.height * 0.15}
            fill="url(#ufo-body)"
            stroke={COLOR_VARIANTS.BLUE}
            strokeWidth="1"
          />
          
          {/* Dome */}
          <ellipse
            cx={currentSize.width / 2}
            cy={currentSize.height * 0.35}
            rx={currentSize.width * 0.2}
            ry={currentSize.height * 0.15}
            fill="url(#ufo-dome)"
            stroke={COLOR_VARIANTS.BLUE}
            strokeWidth="1"
          />
          
          {/* Center Ring */}
          <ellipse
            cx={currentSize.width / 2}
            cy={currentSize.height * 0.45}
            rx={currentSize.width * 0.25}
            ry={currentSize.height * 0.08}
            fill="none"
            stroke={COLOR_VARIANTS.BLUE}
            strokeWidth="1.5"
          />
        </g>

        {/* Static Lights */}
        <g ref={lightsRef}>
          <circle
            cx={currentSize.width * 0.25}
            cy={currentSize.height * 0.45}
            r="2"
            fill={COLOR_VARIANTS.PINK}
            opacity="0.6"
          />
          <circle
            cx={currentSize.width * 0.4}
            cy={currentSize.height * 0.42}
            r="2"
            fill={COLOR_VARIANTS.BLUE}
            opacity="0.6"
          />
          <circle
            cx={currentSize.width * 0.6}
            cy={currentSize.height * 0.42}
            r="2"
            fill={COLOR_VARIANTS.PINK}
            opacity="0.6"
          />
          <circle
            cx={currentSize.width * 0.75}
            cy={currentSize.height * 0.45}
            r="2"
            fill={COLOR_VARIANTS.BLUE}
            opacity="0.6"
          />
        </g>
      </svg>
    </div>
  );
}

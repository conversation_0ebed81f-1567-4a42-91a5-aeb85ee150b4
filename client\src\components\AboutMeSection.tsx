import { useEffect, useRef, useCallback } from "react";
import { useGS<PERSON> } from '@/hooks/useGSAP';
import { useErrorHandler } from '@/components/ErrorBoundary';
import { getAccessibilityConfig } from '@/utils';
import {
  ANIMATION_DURATIONS,
  ANIMATION_EASINGS,
  COLOR_VARIANTS,
  Z_INDEX
} from '@/constants';

export default function AboutMeSection() {
  const sectionRef = useRef<HTMLElement>(null);
  const scanLineRef = useRef<HTMLDivElement>(null);
  const brainOutlineRef = useRef<HTMLDivElement>(null);
  const contentRefs = useRef<(HTMLElement | null)[]>([]);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const hasAnimated = useRef(false);
  
  const { gsap, animate, isAvailable } = useGSAP();
  const { handleError } = useErrorHandler();

  // Hide all content immediately on mount to prevent flash of visible content
  const hideContentInitially = useCallback(() => {
    if (!gsap || !isAvailable) return;

    try {
      // Hide all content elements immediately
      contentRefs.current.forEach((element) => {
        if (element) {
          gsap.set(element, {
            opacity: 0,
            y: 30,
            filter: 'blur(2px)',
            visibility: 'hidden' // Extra insurance against visibility
          });
        }
      });

      // Also hide brain outline initially
      if (brainOutlineRef.current) {
        gsap.set(brainOutlineRef.current, { opacity: 0 });
      }

      // Scanner line should start hidden
      if (scanLineRef.current) {
        gsap.set(scanLineRef.current, { opacity: 0 });
      }
    } catch (error) {
      handleError(error as Error);
    }
  }, [gsap, isAvailable, handleError]);

  const initializeAnimations = useCallback(() => {
    try {
      if (!isAvailable || !gsap || !sectionRef.current || hasAnimated.current) return;

      const accessibility = getAccessibilityConfig();

      if (accessibility.reduceMotion) {
        // Set final states immediately for reduced motion
        if (scanLineRef.current) {
          gsap.set(scanLineRef.current, { opacity: 0 });
        }
        if (brainOutlineRef.current) {
          gsap.set(brainOutlineRef.current, { opacity: 0.15 });
        }
        contentRefs.current.forEach((element) => {
          if (element) {
            gsap.set(element, { opacity: 1, y: 0, x: 0 });
          }
        });
        return;
      }

      // Create ScrollTrigger for the entire animation sequence
      gsap.timeline().to({}, {
        duration: 0,
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top 70%",
          once: true,
          onEnter: () => {
            if (hasAnimated.current) return;
            hasAnimated.current = true;
            playAnimationSequence();
          }
        }
      });

    } catch (error) {
      handleError(error as Error);
    }
  }, [isAvailable, gsap, handleError]);

  const playAnimationSequence = useCallback(() => {
    if (!gsap || !isAvailable) return;

    const masterTimeline = gsap.timeline();

    // Phase 1: Robot Eye Scanner Animation (1.8 seconds total)
    if (scanLineRef.current) {
      masterTimeline
        .set(scanLineRef.current, {
          opacity: 1,
          translateX: '-100%',
          scaleX: 0.1,
          willChange: 'transform'
        })
        // Scanner sweep across screen
        .to(scanLineRef.current, {
          translateX: '100vw',
          scaleX: 1,
          duration: 1.5,
          ease: 'power2.inOut',
          boxShadow: '0 0 30px #4A90E2, 0 0 60px #4A90E2, 0 0 90px #4A90E2'
        }, 0)
        // Scanner fade out
        .to(scanLineRef.current, {
          opacity: 0,
          duration: 0.3,
          ease: ANIMATION_EASINGS.POWER2_OUT,
          onComplete: () => {
            // Remove will-change after animation
            if (scanLineRef.current) {
              scanLineRef.current.style.willChange = 'auto';
            }
          }
        }, 1.5);
    }

    // Phase 2: AI Brain Outline Animation (starts during scanner, continues after)
    if (brainOutlineRef.current) {
      masterTimeline
        .set(brainOutlineRef.current, { opacity: 0, scale: 0.8 }, 0.6)
        .to(brainOutlineRef.current, {
          opacity: 0.15,
          scale: 1,
          duration: 0.8,
          ease: ANIMATION_EASINGS.POWER2_OUT
        }, 0.6)
        // Pulse sequence (starts after scanner completes)
        .to(brainOutlineRef.current, {
          opacity: 0.25,
          scale: 1.05,
          filter: 'brightness(1.3)',
          duration: 0.3,
          ease: ANIMATION_EASINGS.POWER2_IN_OUT,
          yoyo: true,
          repeat: 3 // 2 complete pulses
        }, 2.0)
        .to(brainOutlineRef.current, {
          opacity: 0.1,
          scale: 1,
          filter: 'brightness(1)',
          duration: 0.5,
          ease: ANIMATION_EASINGS.POWER2_OUT
        }, 3.8);
    }

    // Phase 3: Content Reveal (starts ONLY after scanner completely finishes)
    contentRefs.current.forEach((element, index) => {
      if (element) {
        // Set initial state (remove visibility hidden for animation)
        masterTimeline.set(element, {
          opacity: 0,
          y: 30,
          x: 0,
          filter: 'blur(2px)',
          visibility: 'visible', // Make sure it's visible for animation
          willChange: 'transform'
        }, 0);

        // Calculate start time: scanner finishes at 1.8s, add 0.3s buffer, then stagger
        const contentStartTime = 2.1 + (index * 0.12);

        // Special handling for the main title (index 0)
        if (index === 0) {
          masterTimeline
            // Title appears with dramatic effect
            .to(element, {
              opacity: 1,
              y: 0,
              filter: 'blur(0px)',
              duration: 0.8,
              ease: ANIMATION_EASINGS.BACK_OUT,
              onComplete: () => {
                if (element) element.style.willChange = 'auto';
              }
            }, contentStartTime);
        } else {
          // Other content with subtle glitch effect
          masterTimeline
            // Brief glitch flicker
            .to(element, {
              opacity: gsap.utils.random(0.4, 0.7),
              x: gsap.utils.random(-3, 3),
              duration: 0.04,
              ease: 'none'
            }, contentStartTime)
            .to(element, {
              opacity: 0,
              x: gsap.utils.random(-5, 5),
              duration: 0.04,
              ease: 'none'
            }, contentStartTime + 0.04)
            .to(element, {
              opacity: gsap.utils.random(0.6, 0.9),
              x: gsap.utils.random(-2, 2),
              duration: 0.04,
              ease: 'none'
            }, contentStartTime + 0.08)
            // Final smooth reveal
            .to(element, {
              opacity: 1,
              y: 0,
              x: 0,
              filter: 'blur(0px)',
              duration: 0.6,
              ease: ANIMATION_EASINGS.POWER2_OUT,
              onComplete: () => {
                if (element) element.style.willChange = 'auto';
              }
            }, contentStartTime + 0.12);
        }
      }
    });

  }, [gsap, isAvailable]);

  // Hide content immediately when GSAP becomes available
  useEffect(() => {
    if (isAvailable) {
      // Hide content immediately to prevent flash
      hideContentInitially();
    }
  }, [isAvailable, hideContentInitially]);

  // Initialize animations after content is hidden
  useEffect(() => {
    if (isAvailable) {
      // Small delay to ensure DOM is ready and content is hidden
      const timer = setTimeout(initializeAnimations, 150);
      return () => clearTimeout(timer);
    }
  }, [isAvailable, initializeAnimations]);

  return (
    <section 
      ref={sectionRef}
      className="min-h-screen bg-[#0F0F0F] py-20 px-4 relative overflow-hidden"
      id="about"
    >
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-[#1A1A1A] via-[#0F0F0F] to-[#1A1A1A] opacity-80" />
      
      {/* Robot Eye Scan Line */}
      <div
        ref={scanLineRef}
        className="fixed top-0 left-0 w-1 h-full opacity-0 pointer-events-none"
        style={{
          background: 'linear-gradient(to bottom, transparent 0%, #4A90E2 20%, #4A90E2 80%, transparent 100%)',
          filter: 'blur(1px)',
          zIndex: Z_INDEX.CONTENT + 5,
          transform: 'translateX(-100%)', // Use transform instead of left positioning
          willChange: 'transform' // Optimize for animations
        }}
      />

      {/* AI Brain Outline */}
      <div
        ref={brainOutlineRef}
        className="absolute inset-0 opacity-0 pointer-events-none"
        style={{ zIndex: Z_INDEX.BACKGROUND + 1 }}
      >
        <svg
          viewBox="0 0 400 400"
          className="w-full h-full max-w-md mx-auto"
          style={{
            filter: 'drop-shadow(0 0 20px #4A90E2)',
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)'
          }}
        >
          <path
            d="M200 50 C250 50, 300 80, 320 130 C340 180, 320 220, 300 250 C280 280, 240 300, 200 300 C160 300, 120 280, 100 250 C80 220, 60 180, 80 130 C100 80, 150 50, 200 50 Z"
            stroke="#4A90E2"
            strokeWidth="2"
            fill="none"
            opacity="0.3"
          />
          {/* Neural network connections */}
          <g stroke="#4A90E2" strokeWidth="1" opacity="0.2">
            <line x1="120" y1="150" x2="180" y2="180" />
            <line x1="180" y1="180" x2="220" y2="160" />
            <line x1="220" y1="160" x2="280" y2="190" />
            <line x1="150" y1="200" x2="200" y2="220" />
            <line x1="200" y1="220" x2="250" y2="200" />
            <line x1="180" y1="120" x2="220" y2="140" />
          </g>
          {/* Neural nodes */}
          <g fill="#4A90E2" opacity="0.4">
            <circle cx="120" cy="150" r="3" />
            <circle cx="180" cy="180" r="3" />
            <circle cx="220" cy="160" r="3" />
            <circle cx="280" cy="190" r="3" />
            <circle cx="150" cy="200" r="3" />
            <circle cx="200" cy="220" r="3" />
            <circle cx="250" cy="200" r="3" />
            <circle cx="180" cy="120" r="3" />
            <circle cx="220" cy="140" r="3" />
          </g>
        </svg>
      </div>
      
      <div className="max-w-4xl mx-auto relative" style={{ zIndex: Z_INDEX.CONTENT }}>
        {/* Section Title */}
        <div className="text-center mb-16">
          <h2
            ref={(el) => el && (contentRefs.current[0] = el)}
            className="text-4xl md:text-5xl lg:text-6xl font-black text-[#F5F5F5] mb-6 tracking-wide"
            style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
            data-content-index="0"
          >
            ABOUT ME
          </h2>
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16">
          {/* Left Column - Personal Info */}
          <div className="space-y-8">
            <div>
              <h3
                ref={(el) => el && (contentRefs.current[2] = el)}
                className="text-2xl md:text-3xl font-bold text-[#FF3366] mb-4 tracking-wide"
                style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
                data-content-index="2"
              >
                The Human Behind the Code
              </h3>
              <p
                ref={(el) => el && (contentRefs.current[3] = el)}
                className="text-gray-300 leading-relaxed text-lg"
                style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
                data-content-index="3"
              >
                I'm a passionate AI innovator who believes that the best digital experiences
                emerge from the intersection of cutting-edge technology and human creativity. With a
                background spanning from AI/ML to immersive web experiences, I craft solutions that
                don't just work but inspire.

                I started out as a Business Analyst, but got tired of writing requirements 
                for other people’s ideas, I wanted to build my own. I've earned 9 Azure certs,
                including AI Engineering, Data Scientist, and Solution Architect, 
                so I could go deep on both the tech and the strategy. 
                I’m not here to play it safe, I’m here to push what’s possible.
              </p>
            </div>

            <div>
              <h4
                ref={(el) => el && (contentRefs.current[4] = el)}
                className="text-xl font-semibold text-[#4A90E2] mb-4"
                style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
                data-content-index="4"
              >
                What Drives Me
              </h4>
              <ul className="space-y-3">
                <li
                  ref={(el) => el && (contentRefs.current[5] = el)}
                  className="text-gray-300 flex items-start"
                  style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
                  data-content-index="5"
                >
                  <span className="text-[#FF3366] mr-3 text-xl">◆</span>
                  Building AI-powered applications that enhance human potential
                </li>
                <li
                  ref={(el) => el && (contentRefs.current[6] = el)}
                  className="text-gray-300 flex items-start"
                  style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
                  data-content-index="6"
                >
                  <span className="text-[#4A90E2] mr-3 text-xl">◇</span>
                  Creating immersive, interactive user experiences with GSAP and modern web tech
                </li>
                <li
                  ref={(el) => el && (contentRefs.current[7] = el)}
                  className="text-gray-300 flex items-start"
                  style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
                  data-content-index="7"
                >
                  <span className="text-[#FF3366] mr-3 text-xl">◈</span>
                  Pushing the edge of what's possible with coding agents and creative tech
                </li>
              </ul>
            </div>
          </div>

          {/* Right Column - Skills & Tech */}
          <div className="space-y-8">
            <div>
              <h3
                ref={(el) => el && (contentRefs.current[8] = el)}
                className="text-2xl md:text-3xl font-bold text-[#4A90E2] mb-4 tracking-wide"
                style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
                data-content-index="8"
              >
                Tech Arsenal
              </h3>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h5
                    ref={(el) => el && (contentRefs.current[9] = el)}
                    className="text-lg font-semibold text-[#F5F5F5] mb-3"
                    style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
                    data-content-index="9"
                  >
                    Frontend
                  </h5>
                  <ul className="space-y-2">
                    <li
                      ref={(el) => el && (contentRefs.current[10] = el)}
                      className="text-gray-400 text-sm"
                      style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
                      data-content-index="10"
                    >
                      React • TypeScript • Next.js
                    </li>
                    <li
                      ref={(el) => el && (contentRefs.current[11] = el)}
                      className="text-gray-400 text-sm"
                      style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
                      data-content-index="11"
                    >
                      GSAP • Three.js • Tailwind
                    </li>
                    <li
                      ref={(el) => el && (contentRefs.current[12] = el)}
                      className="text-gray-400 text-sm"
                      style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
                      data-content-index="12"
                    >
                     WebGL • Framer Motion 
                    </li>
                  </ul>
                </div>
                <div>
                  <h5
                    ref={(el) => el && (contentRefs.current[13] = el)}
                    className="text-lg font-semibold text-[#F5F5F5] mb-3"
                    style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
                    data-content-index="13"
                  >
                    Backend & AI
                  </h5>
                  <ul className="space-y-2">
                    <li
                      ref={(el) => el && (contentRefs.current[14] = el)}
                      className="text-gray-400 text-sm"
                      style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
                      data-content-index="14"
                    >
                      Node.js • PostgreSQL
                    </li>
                    <li
                      ref={(el) => el && (contentRefs.current[15] = el)}
                      className="text-gray-400 text-sm"
                      style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
                      data-content-index="15"
                    >
                      LangChain • Azure AI
                    </li>
                    <li
                      ref={(el) => el && (contentRefs.current[16] = el)}
                      className="text-gray-400 text-sm"
                      style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
                      data-content-index="16"
                    >
                      Cursor • Replit • Augment
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div>
              <h4
                ref={(el) => el && (contentRefs.current[17] = el)}
                className="text-xl font-semibold text-[#FF3366] mb-4"
                style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
                data-content-index="17"
              >
                Build Learn Repeat
              </h4>
              <p
                ref={(el) => el && (contentRefs.current[18] = el)}
                className="text-gray-300 leading-relaxed"
                style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
                data-content-index="18"
              >
                When I’m not coding, I’m still deep in the tech world. I mess around with stuff like NVIDIA Omniverse,
                robotics ideas, and AI agent tools just to see what’s possible. Half the time I’m reading up on the latest AI
                research or testing out new dev environments. Outside of all that, I’ve been learning Mandarin
                and when I need to chill, I get back into music production using Maschine. 
                I treat my free time like my own R&D lab where I can explore the intersection of tech and art in my own unique way.
              </p>
            </div>

            <div>
              <h4
                ref={(el) => el && (contentRefs.current[19] = el)}
                className="text-xl font-semibold text-[#4A90E2] mb-4"
                style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
                data-content-index="19"
              >
                Let's Build Something Amazing
              </h4>
              <p
                ref={(el) => el && (contentRefs.current[20] = el)}
                className="text-gray-300 leading-relaxed"
                style={{ opacity: 0, visibility: 'hidden' }} // Start hidden
                data-content-index="20"
              >
                I'm always excited to collaborate on projects that push boundaries and create meaningful
                impact. Whether it's an AI-powered platform, an immersive web experience, or something
                completely unprecedented, let's explore what's possible together!
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
} 